#!/usr/bin/env python3
"""
Alerting and Notifications System

Features:
🔔 1. Multi-Channel Notifications
- Console notifications
- File logging
- Email alerts
- Telegram notifications
- Slack integration
- SMS alerts (future)

📊 2. Signal Alerts
- Trade signal notifications
- Entry/exit alerts
- Strategy performance alerts
- Market condition changes

⚠️ 3. Risk Alerts
- Position limit breaches
- Loss threshold alerts
- Margin call warnings
- Portfolio risk alerts

📈 4. Performance Alerts
- P&L milestone notifications
- Win/loss streak alerts
- Daily target achievements
- Drawdown warnings

🎯 5. System Alerts
- Agent status changes
- Data feed issues
- Connection problems
- Error notifications
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
import json
import yaml
from dataclasses import dataclass, asdict
from enum import Enum
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

logger = logging.getLogger(__name__)

class AlertType(Enum):
    """Alert types"""
    SIGNAL = "SIGNAL"
    RISK = "RISK"
    PERFORMANCE = "PERFORMANCE"
    SYSTEM = "SYSTEM"
    ERROR = "ERROR"
    INFO = "INFO"

class AlertPriority(Enum):
    """Alert priority levels"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class NotificationChannel(Enum):
    """Notification channels"""
    CONSOLE = "CONSOLE"
    FILE = "FILE"
    EMAIL = "EMAIL"
    TELEGRAM = "TELEGRAM"
    SLACK = "SLACK"
    SMS = "SMS"

@dataclass
class Alert:
    """Alert data structure"""
    alert_id: str
    alert_type: AlertType
    priority: AlertPriority
    title: str
    message: str
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    channels: List[NotificationChannel]
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None

@dataclass
class NotificationConfig:
    """Notification configuration"""
    enabled: bool
    channel: NotificationChannel
    settings: Dict[str, Any]

class AlertingNotificationsSystem:
    """Comprehensive alerting and notifications system"""
    
    def __init__(self, config_path: str = "config/alerting_config.yaml"):
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.is_running = False
        
        # Alert storage
        self.alerts: Dict[str, Alert] = {}
        self.alert_counter = 1
        self.alert_history: List[Alert] = []
        
        # Notification channels
        self.notification_channels: Dict[NotificationChannel, NotificationConfig] = {}
        
        # Alert rules and filters
        self.alert_rules: List[Dict[str, Any]] = []
        self.alert_filters: List[Callable] = []
        
        # Rate limiting
        self.rate_limits: Dict[str, Dict[str, Any]] = {}
        
        logger.info("🔔 [INIT] Alerting and Notifications System initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the alerting system"""
        try:
            await self._load_config()
            await self._setup_notification_channels()
            await self._setup_alert_rules()
            
            logger.info("✅ [SUCCESS] Alerting and Notifications System initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to initialize alerting system: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from YAML file"""
        default_config = {
            'notifications': {
                'console': {'enabled': True},
                'file': {
                    'enabled': True,
                    'log_file': 'logs/alerts.log',
                    'max_size_mb': 100,
                    'backup_count': 5
                },
                'email': {
                    'enabled': False,
                    'smtp_server': 'smtp.gmail.com',
                    'smtp_port': 587,
                    'username': '',
                    'password': '',
                    'from_email': '',
                    'to_emails': []
                },
                'telegram': {
                    'enabled': False,
                    'bot_token': '',
                    'chat_ids': []
                },
                'slack': {
                    'enabled': False,
                    'webhook_url': '',
                    'channel': '#trading-alerts'
                }
            },
            'alert_rules': {
                'signal_alerts': {
                    'enabled': True,
                    'min_confidence': 0.7,
                    'channels': ['console', 'file']
                },
                'risk_alerts': {
                    'enabled': True,
                    'loss_threshold_percent': 5.0,
                    'position_limit_percent': 80.0,
                    'channels': ['console', 'file', 'email']
                },
                'performance_alerts': {
                    'enabled': True,
                    'profit_milestones': [5000, 10000, 25000],
                    'loss_milestones': [-2000, -5000, -10000],
                    'channels': ['console', 'file']
                },
                'system_alerts': {
                    'enabled': True,
                    'error_threshold': 5,
                    'channels': ['console', 'file', 'email']
                }
            },
            'rate_limiting': {
                'enabled': True,
                'max_alerts_per_minute': 10,
                'max_alerts_per_hour': 100,
                'duplicate_suppression_minutes': 5
            }
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    file_config = yaml.safe_load(f)
                    self.config = {**default_config, **file_config}
            except Exception as e:
                logger.warning(f"[WARNING] Failed to load config file, using defaults: {e}")
                self.config = default_config
        else:
            self.config = default_config
            logger.info("[CONFIG] Using default configuration")
    
    async def _setup_notification_channels(self):
        """Setup notification channels"""
        try:
            notifications_config = self.config['notifications']
            
            # Console notifications
            if notifications_config['console']['enabled']:
                self.notification_channels[NotificationChannel.CONSOLE] = NotificationConfig(
                    enabled=True,
                    channel=NotificationChannel.CONSOLE,
                    settings=notifications_config['console']
                )
            
            # File notifications
            if notifications_config['file']['enabled']:
                self.notification_channels[NotificationChannel.FILE] = NotificationConfig(
                    enabled=True,
                    channel=NotificationChannel.FILE,
                    settings=notifications_config['file']
                )
                # Ensure log directory exists
                log_file = Path(notifications_config['file']['log_file'])
                log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Email notifications
            if notifications_config['email']['enabled']:
                self.notification_channels[NotificationChannel.EMAIL] = NotificationConfig(
                    enabled=True,
                    channel=NotificationChannel.EMAIL,
                    settings=notifications_config['email']
                )
            
            # Telegram notifications
            if notifications_config['telegram']['enabled']:
                self.notification_channels[NotificationChannel.TELEGRAM] = NotificationConfig(
                    enabled=True,
                    channel=NotificationChannel.TELEGRAM,
                    settings=notifications_config['telegram']
                )
            
            # Slack notifications
            if notifications_config['slack']['enabled']:
                self.notification_channels[NotificationChannel.SLACK] = NotificationConfig(
                    enabled=True,
                    channel=NotificationChannel.SLACK,
                    settings=notifications_config['slack']
                )
            
            logger.info(f"📡 [CHANNELS] Configured {len(self.notification_channels)} notification channels")
            
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to setup notification channels: {e}")
    
    async def _setup_alert_rules(self):
        """Setup alert rules"""
        try:
            alert_rules_config = self.config['alert_rules']
            
            # Signal alert rules
            if alert_rules_config['signal_alerts']['enabled']:
                self.alert_rules.append({
                    'type': AlertType.SIGNAL,
                    'config': alert_rules_config['signal_alerts']
                })
            
            # Risk alert rules
            if alert_rules_config['risk_alerts']['enabled']:
                self.alert_rules.append({
                    'type': AlertType.RISK,
                    'config': alert_rules_config['risk_alerts']
                })
            
            # Performance alert rules
            if alert_rules_config['performance_alerts']['enabled']:
                self.alert_rules.append({
                    'type': AlertType.PERFORMANCE,
                    'config': alert_rules_config['performance_alerts']
                })
            
            # System alert rules
            if alert_rules_config['system_alerts']['enabled']:
                self.alert_rules.append({
                    'type': AlertType.SYSTEM,
                    'config': alert_rules_config['system_alerts']
                })
            
            logger.info(f"📋 [RULES] Configured {len(self.alert_rules)} alert rules")
            
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to setup alert rules: {e}")
    
    async def send_alert(self, alert_type: AlertType, priority: AlertPriority, title: str, 
                        message: str, source: str = "SYSTEM", data: Dict[str, Any] = None,
                        channels: List[NotificationChannel] = None) -> str:
        """Send an alert through configured channels"""
        try:
            # Generate alert ID
            alert_id = f"ALERT_{self.alert_counter:06d}"
            self.alert_counter += 1
            
            # Default data and channels
            if data is None:
                data = {}
            if channels is None:
                channels = [NotificationChannel.CONSOLE, NotificationChannel.FILE]
            
            # Create alert
            alert = Alert(
                alert_id=alert_id,
                alert_type=alert_type,
                priority=priority,
                title=title,
                message=message,
                timestamp=datetime.now(),
                source=source,
                data=data,
                channels=channels
            )
            
            # Check rate limiting
            if not await self._check_rate_limits(alert):
                logger.warning(f"[RATE LIMIT] Alert {alert_id} suppressed due to rate limiting")
                return alert_id
            
            # Store alert
            self.alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Send through configured channels
            await self._send_through_channels(alert)
            
            logger.info(f"🔔 [ALERT] Alert sent: {alert_id} - {title}")
            return alert_id
            
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send alert: {e}")
            return ""
    
    async def _check_rate_limits(self, alert: Alert) -> bool:
        """Check if alert should be rate limited"""
        try:
            if not self.config['rate_limiting']['enabled']:
                return True
            
            now = datetime.now()
            rate_key = f"{alert.alert_type.value}_{alert.source}"
            
            # Initialize rate limit tracking for this key
            if rate_key not in self.rate_limits:
                self.rate_limits[rate_key] = {
                    'minute_count': 0,
                    'hour_count': 0,
                    'last_minute': now.minute,
                    'last_hour': now.hour,
                    'last_alert_time': None
                }
            
            rate_data = self.rate_limits[rate_key]
            
            # Reset counters if time period changed
            if now.minute != rate_data['last_minute']:
                rate_data['minute_count'] = 0
                rate_data['last_minute'] = now.minute
            
            if now.hour != rate_data['last_hour']:
                rate_data['hour_count'] = 0
                rate_data['last_hour'] = now.hour
            
            # Check rate limits
            max_per_minute = self.config['rate_limiting']['max_alerts_per_minute']
            max_per_hour = self.config['rate_limiting']['max_alerts_per_hour']
            
            if rate_data['minute_count'] >= max_per_minute:
                return False
            
            if rate_data['hour_count'] >= max_per_hour:
                return False
            
            # Check duplicate suppression
            if rate_data['last_alert_time']:
                suppression_minutes = self.config['rate_limiting']['duplicate_suppression_minutes']
                time_diff = (now - rate_data['last_alert_time']).total_seconds() / 60
                if time_diff < suppression_minutes:
                    # Check if this is a duplicate alert
                    if self._is_duplicate_alert(alert, rate_key):
                        return False
            
            # Update counters
            rate_data['minute_count'] += 1
            rate_data['hour_count'] += 1
            rate_data['last_alert_time'] = now
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [ERROR] Rate limit check failed: {e}")
            return True  # Allow alert if check fails
    
    def _is_duplicate_alert(self, alert: Alert, rate_key: str) -> bool:
        """Check if this is a duplicate alert"""
        try:
            # Simple duplicate check based on title and message
            for recent_alert in reversed(self.alert_history[-10:]):  # Check last 10 alerts
                if (recent_alert.title == alert.title and 
                    recent_alert.message == alert.message and
                    recent_alert.source == alert.source):
                    return True
            return False
        except Exception as e:
            logger.error(f"❌ [ERROR] Duplicate check failed: {e}")
            return False

    async def _send_through_channels(self, alert: Alert):
        """Send alert through configured channels"""
        try:
            for channel in alert.channels:
                if channel in self.notification_channels:
                    channel_config = self.notification_channels[channel]
                    if channel_config.enabled:
                        await self._send_to_channel(alert, channel, channel_config)
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send through channels: {e}")

    async def _send_to_channel(self, alert: Alert, channel: NotificationChannel, config: NotificationConfig):
        """Send alert to specific channel"""
        try:
            if channel == NotificationChannel.CONSOLE:
                await self._send_console_notification(alert)
            elif channel == NotificationChannel.FILE:
                await self._send_file_notification(alert, config.settings)
            elif channel == NotificationChannel.EMAIL:
                await self._send_email_notification(alert, config.settings)
            elif channel == NotificationChannel.TELEGRAM:
                await self._send_telegram_notification(alert, config.settings)
            elif channel == NotificationChannel.SLACK:
                await self._send_slack_notification(alert, config.settings)
            else:
                logger.warning(f"[WARNING] Unsupported notification channel: {channel}")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send to {channel}: {e}")

    async def _send_console_notification(self, alert: Alert):
        """Send console notification"""
        try:
            priority_emoji = {
                AlertPriority.LOW: "ℹ️",
                AlertPriority.MEDIUM: "⚠️",
                AlertPriority.HIGH: "🚨",
                AlertPriority.CRITICAL: "🔥"
            }

            type_emoji = {
                AlertType.SIGNAL: "📈",
                AlertType.RISK: "⚠️",
                AlertType.PERFORMANCE: "📊",
                AlertType.SYSTEM: "🔧",
                AlertType.ERROR: "❌",
                AlertType.INFO: "ℹ️"
            }

            emoji = priority_emoji.get(alert.priority, "📢")
            type_icon = type_emoji.get(alert.alert_type, "📢")

            console_message = (
                f"\n{emoji} {type_icon} [{alert.priority.value}] {alert.title}\n"
                f"📅 {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🏷️ Source: {alert.source}\n"
                f"💬 {alert.message}\n"
                f"🆔 Alert ID: {alert.alert_id}\n"
                + "─" * 60
            )

            # Use appropriate log level based on priority
            if alert.priority == AlertPriority.CRITICAL:
                logger.critical(console_message)
            elif alert.priority == AlertPriority.HIGH:
                logger.error(console_message)
            elif alert.priority == AlertPriority.MEDIUM:
                logger.warning(console_message)
            else:
                logger.info(console_message)

        except Exception as e:
            logger.error(f"❌ [ERROR] Console notification failed: {e}")

    async def _send_file_notification(self, alert: Alert, settings: Dict[str, Any]):
        """Send file notification"""
        try:
            log_file = Path(settings['log_file'])
            log_file.parent.mkdir(parents=True, exist_ok=True)

            log_entry = {
                'timestamp': alert.timestamp.isoformat(),
                'alert_id': alert.alert_id,
                'type': alert.alert_type.value,
                'priority': alert.priority.value,
                'title': alert.title,
                'message': alert.message,
                'source': alert.source,
                'data': alert.data
            }

            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry) + '\n')

        except Exception as e:
            logger.error(f"❌ [ERROR] File notification failed: {e}")

    async def _send_email_notification(self, alert: Alert, settings: Dict[str, Any]):
        """Send email notification"""
        try:
            if not settings.get('to_emails'):
                logger.warning("[WARNING] No email recipients configured")
                return

            # Create email message
            msg = MIMEMultipart()
            msg['From'] = settings['from_email']
            msg['To'] = ', '.join(settings['to_emails'])
            msg['Subject'] = f"[{alert.priority.value}] {alert.title}"

            # Email body
            body = f"""
Trading Alert Notification

Alert ID: {alert.alert_id}
Type: {alert.alert_type.value}
Priority: {alert.priority.value}
Timestamp: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
Source: {alert.source}

Message:
{alert.message}

Additional Data:
{json.dumps(alert.data, indent=2) if alert.data else 'None'}

---
This is an automated message from the Options Trading System.
"""

            msg.attach(MIMEText(body, 'plain'))

            # Send email
            server = smtplib.SMTP(settings['smtp_server'], settings['smtp_port'])
            server.starttls()
            server.login(settings['username'], settings['password'])
            text = msg.as_string()
            server.sendmail(settings['from_email'], settings['to_emails'], text)
            server.quit()

            logger.info(f"📧 [EMAIL] Alert sent via email: {alert.alert_id}")

        except Exception as e:
            logger.error(f"❌ [ERROR] Email notification failed: {e}")

    # Convenience methods for different alert types

    async def send_signal_alert(self, title: str, message: str, confidence: float = 0.0,
                               symbol: str = "", strategy: str = "", data: Dict[str, Any] = None):
        """Send a trading signal alert"""
        try:
            priority = AlertPriority.HIGH if confidence >= 0.8 else AlertPriority.MEDIUM

            if data is None:
                data = {}
            data.update({
                'confidence': confidence,
                'symbol': symbol,
                'strategy': strategy
            })

            channels = [NotificationChannel.CONSOLE, NotificationChannel.FILE]

            # Add email for high confidence signals
            if confidence >= 0.8 and NotificationChannel.EMAIL in self.notification_channels:
                channels.append(NotificationChannel.EMAIL)

            return await self.send_alert(
                alert_type=AlertType.SIGNAL,
                priority=priority,
                title=title,
                message=message,
                source="SIGNAL_AGENT",
                data=data,
                channels=channels
            )

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send signal alert: {e}")
            return ""

    async def send_risk_alert(self, title: str, message: str, risk_level: str = "MEDIUM",
                             current_value: float = 0.0, threshold: float = 0.0,
                             data: Dict[str, Any] = None):
        """Send a risk management alert"""
        try:
            priority_map = {
                "LOW": AlertPriority.LOW,
                "MEDIUM": AlertPriority.MEDIUM,
                "HIGH": AlertPriority.HIGH,
                "CRITICAL": AlertPriority.CRITICAL
            }
            priority = priority_map.get(risk_level.upper(), AlertPriority.MEDIUM)

            if data is None:
                data = {}
            data.update({
                'risk_level': risk_level,
                'current_value': current_value,
                'threshold': threshold
            })

            channels = [NotificationChannel.CONSOLE, NotificationChannel.FILE]

            # Add email for high/critical risk alerts
            if priority in [AlertPriority.HIGH, AlertPriority.CRITICAL]:
                if NotificationChannel.EMAIL in self.notification_channels:
                    channels.append(NotificationChannel.EMAIL)
                if NotificationChannel.TELEGRAM in self.notification_channels:
                    channels.append(NotificationChannel.TELEGRAM)

            return await self.send_alert(
                alert_type=AlertType.RISK,
                priority=priority,
                title=title,
                message=message,
                source="RISK_AGENT",
                data=data,
                channels=channels
            )

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send risk alert: {e}")
            return ""

    async def send_performance_alert(self, title: str, message: str, pnl: float = 0.0,
                                   pnl_percent: float = 0.0, milestone: bool = False,
                                   data: Dict[str, Any] = None):
        """Send a performance alert"""
        try:
            if milestone or abs(pnl_percent) >= 5.0:
                priority = AlertPriority.HIGH
            elif abs(pnl_percent) >= 2.0:
                priority = AlertPriority.MEDIUM
            else:
                priority = AlertPriority.LOW

            if data is None:
                data = {}
            data.update({
                'pnl': pnl,
                'pnl_percent': pnl_percent,
                'milestone': milestone
            })

            channels = [NotificationChannel.CONSOLE, NotificationChannel.FILE]

            # Add additional channels for milestones
            if milestone:
                if NotificationChannel.TELEGRAM in self.notification_channels:
                    channels.append(NotificationChannel.TELEGRAM)
                if NotificationChannel.SLACK in self.notification_channels:
                    channels.append(NotificationChannel.SLACK)

            return await self.send_alert(
                alert_type=AlertType.PERFORMANCE,
                priority=priority,
                title=title,
                message=message,
                source="PERFORMANCE_AGENT",
                data=data,
                channels=channels
            )

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send performance alert: {e}")
            return ""

    async def send_system_alert(self, title: str, message: str, severity: str = "INFO",
                               component: str = "SYSTEM", data: Dict[str, Any] = None):
        """Send a system alert"""
        try:
            severity_map = {
                "INFO": AlertPriority.LOW,
                "WARNING": AlertPriority.MEDIUM,
                "ERROR": AlertPriority.HIGH,
                "CRITICAL": AlertPriority.CRITICAL
            }
            priority = severity_map.get(severity.upper(), AlertPriority.LOW)

            if data is None:
                data = {}
            data.update({
                'severity': severity,
                'component': component
            })

            channels = [NotificationChannel.CONSOLE, NotificationChannel.FILE]

            # Add email for errors and critical issues
            if priority in [AlertPriority.HIGH, AlertPriority.CRITICAL]:
                if NotificationChannel.EMAIL in self.notification_channels:
                    channels.append(NotificationChannel.EMAIL)

            return await self.send_alert(
                alert_type=AlertType.SYSTEM,
                priority=priority,
                title=title,
                message=message,
                source=component,
                data=data,
                channels=channels
            )

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to send system alert: {e}")
            return ""

    def get_alert_summary(self) -> Dict[str, Any]:
        """Get summary of alerts"""
        try:
            now = datetime.now()
            last_hour = now - timedelta(hours=1)
            last_day = now - timedelta(days=1)

            # Count alerts by time period
            alerts_last_hour = [a for a in self.alert_history if a.timestamp >= last_hour]
            alerts_last_day = [a for a in self.alert_history if a.timestamp >= last_day]

            # Count by type
            type_counts = {}
            priority_counts = {}

            for alert in alerts_last_day:
                type_counts[alert.alert_type.value] = type_counts.get(alert.alert_type.value, 0) + 1
                priority_counts[alert.priority.value] = priority_counts.get(alert.priority.value, 0) + 1

            return {
                'total_alerts': len(self.alert_history),
                'alerts_last_hour': len(alerts_last_hour),
                'alerts_last_day': len(alerts_last_day),
                'active_alerts': len([a for a in self.alerts.values() if not a.acknowledged]),
                'type_counts_24h': type_counts,
                'priority_counts_24h': priority_counts,
                'configured_channels': list(self.notification_channels.keys()),
                'rate_limiting_enabled': self.config['rate_limiting']['enabled']
            }

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to get alert summary: {e}")
            return {}

    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str = "SYSTEM"):
        """Acknowledge an alert"""
        try:
            if alert_id in self.alerts:
                alert = self.alerts[alert_id]
                alert.acknowledged = True
                alert.acknowledged_by = acknowledged_by
                alert.acknowledged_at = datetime.now()

                logger.info(f"✅ [ACK] Alert acknowledged: {alert_id} by {acknowledged_by}")
                return True
            else:
                logger.warning(f"[WARNING] Alert not found for acknowledgment: {alert_id}")
                return False

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to acknowledge alert: {e}")
            return False

    async def cleanup(self):
        """Cleanup the alerting system"""
        try:
            logger.info("🛑 [CLEANUP] Stopping Alerting and Notifications System...")
            self.is_running = False

            # Save alert history
            await self._save_alert_history()

            logger.info("✅ [CLEANUP] Alerting and Notifications System stopped successfully")
        except Exception as e:
            logger.error(f"❌ [ERROR] Error during cleanup: {e}")

    async def _save_alert_history(self):
        """Save alert history to file"""
        try:
            history_file = Path("data/alerts/alert_history.json")
            history_file.parent.mkdir(parents=True, exist_ok=True)

            # Convert alerts to serializable format
            history_data = []
            for alert in self.alert_history:
                alert_dict = asdict(alert)
                alert_dict['timestamp'] = alert.timestamp.isoformat()
                if alert.acknowledged_at:
                    alert_dict['acknowledged_at'] = alert.acknowledged_at.isoformat()
                history_data.append(alert_dict)

            with open(history_file, 'w') as f:
                json.dump(history_data, f, indent=2)

            logger.info(f"💾 [SAVE] Alert history saved: {len(history_data)} alerts")

        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to save alert history: {e}")


# Example usage
async def main():
    """Example usage of Alerting and Notifications System"""
    alerting_system = AlertingNotificationsSystem()

    try:
        await alerting_system.initialize()

        # Example signal alert
        await alerting_system.send_signal_alert(
            title="Strong Buy Signal",
            message="NIFTY24800CE showing strong bullish momentum with 85% confidence",
            confidence=0.85,
            symbol="NIFTY24800CE",
            strategy="momentum_breakout"
        )

        # Example risk alert
        await alerting_system.send_risk_alert(
            title="Position Limit Warning",
            message="Portfolio exposure approaching 80% of maximum limit",
            risk_level="HIGH",
            current_value=79.5,
            threshold=80.0
        )

        # Example performance alert
        await alerting_system.send_performance_alert(
            title="Daily Profit Milestone",
            message="Daily P&L has reached ₹5,000 profit target",
            pnl=5000.0,
            pnl_percent=5.0,
            milestone=True
        )

        # Example system alert
        await alerting_system.send_system_alert(
            title="Data Feed Reconnected",
            message="Market data feed has been successfully reconnected after temporary disconnection",
            severity="INFO",
            component="DATA_AGENT"
        )

        # Get alert summary
        summary = alerting_system.get_alert_summary()
        print(f"Alert Summary: {summary}")

    except Exception as e:
        logger.error(f"Error in example: {e}")
    finally:
        await alerting_system.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
