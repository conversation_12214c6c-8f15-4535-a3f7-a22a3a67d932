# System Status Monitor Configuration
# Comprehensive system monitoring and alerting configuration

# Status Update Settings
status_update_interval: 200  # seconds (every 200s as requested)
daily_trades_target: 50      # target number of trades per day

# Alert Thresholds
alert_thresholds:
  max_daily_loss_percent: 5.0      # Maximum daily loss percentage before alert
  min_win_rate: 40.0               # Minimum win rate percentage before alert
  max_drawdown_percent: 10.0       # Maximum drawdown percentage before alert
  memory_usage_mb: 1000            # Maximum memory usage in MB before alert
  cpu_usage_percent: 80.0          # Maximum CPU usage percentage before alert
  max_active_positions: 5          # Maximum number of active positions
  position_size_limit: 20000       # Maximum position size in INR
  daily_capital_usage_percent: 15.0 # Maximum daily capital usage percentage

# Performance Monitoring
performance_monitoring:
  track_real_time_pnl: true
  track_estimated_pnl: true
  calculate_sharpe_ratio: true
  calculate_max_drawdown: true
  track_win_loss_streaks: true
  monitor_trade_duration: true
  track_slippage: true

# Risk Monitoring
risk_monitoring:
  monitor_position_concentration: true
  track_sector_exposure: true
  monitor_greeks_exposure: true
  track_volatility_exposure: true
  monitor_correlation_risk: true

# Notification Settings
notifications:
  enabled: true
  channels:
    - console
    - file
    # - telegram  # Uncomment when Telegram is configured
    # - email     # Uncomment when email is configured
    # - slack     # Uncomment when Slack is configured
  
  milestone_alerts: true
  
  # Notification Thresholds
  thresholds:
    profit_milestone: 5000        # Alert when profit reaches this amount
    loss_milestone: -2000         # Alert when loss reaches this amount
    trade_count_milestone: 10     # Alert every N trades
    win_streak_milestone: 5       # Alert on win streaks
    loss_streak_milestone: 3      # Alert on loss streaks

# Data Storage
data_storage:
  save_status_to_file: true
  save_alerts_to_file: true
  status_file_path: "data/status/system_status.json"
  alerts_file_path: "data/alerts/system_alerts.log"
  historical_data_retention_days: 30
  compress_old_data: true

# Dashboard Settings
dashboard:
  enabled: true
  update_interval: 30           # seconds
  show_real_time_charts: true
  show_performance_metrics: true
  show_risk_metrics: true
  show_system_health: true

# System Health Monitoring
system_health:
  monitor_disk_space: true
  monitor_network_connectivity: true
  monitor_agent_responsiveness: true
  monitor_data_freshness: true
  check_interval: 120           # seconds
  
  # Health Check Thresholds
  thresholds:
    min_disk_space_gb: 5.0
    max_response_time_ms: 5000
    max_data_age_minutes: 10

# Logging Configuration
logging:
  log_level: "INFO"
  log_status_updates: true
  log_performance_metrics: true
  log_alerts: true
  log_system_health: true
  
  # Log File Settings
  log_files:
    status_log: "logs/system_status.log"
    performance_log: "logs/performance.log"
    alerts_log: "logs/alerts.log"
    health_log: "logs/system_health.log"

# Integration Settings
integration:
  # Risk Management Agent Integration
  risk_management:
    enabled: true
    sync_position_data: true
    sync_balance_data: true
    sync_trade_data: true
  
  # Performance Analysis Agent Integration
  performance_analysis:
    enabled: true
    sync_pnl_data: true
    sync_metrics_data: true
  
  # Execution Agent Integration
  execution:
    enabled: true
    sync_order_data: true
    sync_fill_data: true

# Advanced Features
advanced_features:
  # Machine Learning Integration
  ml_integration:
    enabled: false
    predict_performance: false
    anomaly_detection: false
  
  # Real-time Analytics
  real_time_analytics:
    enabled: true
    calculate_live_sharpe: true
    calculate_live_var: true
    track_intraday_drawdown: true
  
  # Stress Testing
  stress_testing:
    enabled: false
    scenarios:
      - name: "market_crash"
        description: "20% market drop scenario"
        parameters:
          market_drop_percent: -20
      - name: "volatility_spike"
        description: "Volatility doubles scenario"
        parameters:
          volatility_multiplier: 2.0

# Backup and Recovery
backup:
  enabled: true
  backup_interval_hours: 6
  backup_location: "backups/system_status"
  max_backup_files: 10
  compress_backups: true

# Security Settings
security:
  encrypt_sensitive_data: false
  mask_account_numbers: true
  audit_trail: true
  access_control: false

# Development and Testing
development:
  debug_mode: false
  test_mode: false
  mock_data: false
  simulation_mode: false
