#!/usr/bin/env python3
"""
Test script for incremental training functionality
"""

import asyncio
import logging
import polars as pl
import numpy as np
from pathlib import Path
import json
import tempfile
import shutil
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_incremental_training():
    """Test the incremental training functionality"""
    
    # Create temporary directory for testing
    test_dir = Path(tempfile.mkdtemp(prefix="test_incremental_"))
    logger.info(f"[TEST] Using temporary directory: {test_dir}")
    
    try:
        # Setup test environment
        data_dir = test_dir / "data"
        features_dir = data_dir / "features" / "1min"
        models_dir = data_dir / "models"
        ai_training_dir = data_dir / "ai_training"
        
        features_dir.mkdir(parents=True, exist_ok=True)
        models_dir.mkdir(parents=True, exist_ok=True)
        ai_training_dir.mkdir(parents=True, exist_ok=True)
        
        # Create sample training data
        logger.info("[TEST] Creating sample training data...")
        sample_data = create_sample_training_data()
        
        # Save sample data as feature files
        nifty_file = features_dir / "NIFTY_1min_features_20241201_120000.parquet"
        banknifty_file = features_dir / "BANKNIFTY_1min_features_20241201_120000.parquet"
        
        sample_data.write_parquet(nifty_file)
        sample_data.write_parquet(banknifty_file)
        
        logger.info(f"[TEST] Created sample data files with {sample_data.height} records each")
        
        # Import and test the AI training agent
        import sys
        sys.path.append(str(Path(__file__).parent))
        
        from agents.options_ai_training_agent import OptionsAITrainingAgent
        
        # Test 1: First training run (should process all data)
        logger.info("[TEST 1] First training run - should process all data")
        agent1 = OptionsAITrainingAgent()
        agent1.data_path = data_dir
        agent1.features_path = features_dir.parent
        agent1.models_path = models_dir
        agent1.ai_training_path = ai_training_dir
        agent1.training_registry_path = ai_training_dir / "training_data_registry.json"
        
        await agent1.initialize(incremental=True)
        result1 = await agent1.start(incremental=True)
        
        logger.info(f"[TEST 1] Result: {result1}")
        
        # Check if registry was created
        registry_exists = agent1.training_registry_path.exists()
        logger.info(f"[TEST 1] Registry created: {registry_exists}")
        
        if registry_exists:
            with open(agent1.training_registry_path, 'r') as f:
                registry_data = json.load(f)
            logger.info(f"[TEST 1] Registry has {len(registry_data.get('processed_data_hashes', {}))} processed data hashes")
        
        await agent1.cleanup()
        
        # Test 2: Second training run (should detect duplicates)
        logger.info("[TEST 2] Second training run - should detect duplicates")
        agent2 = OptionsAITrainingAgent()
        agent2.data_path = data_dir
        agent2.features_path = features_dir.parent
        agent2.models_path = models_dir
        agent2.ai_training_path = ai_training_dir
        agent2.training_registry_path = ai_training_dir / "training_data_registry.json"
        
        await agent2.initialize(incremental=True)
        result2 = await agent2.start(incremental=True)
        
        logger.info(f"[TEST 2] Result: {result2}")
        await agent2.cleanup()
        
        # Test 3: Add new data and run again (should process only new data)
        logger.info("[TEST 3] Adding new data and running again")
        
        # Create new sample data with different timestamp
        new_sample_data = create_sample_training_data(rows=500)  # Different size
        new_nifty_file = features_dir / "NIFTY_1min_features_20241202_120000.parquet"
        new_sample_data.write_parquet(new_nifty_file)
        
        agent3 = OptionsAITrainingAgent()
        agent3.data_path = data_dir
        agent3.features_path = features_dir.parent
        agent3.models_path = models_dir
        agent3.ai_training_path = ai_training_dir
        agent3.training_registry_path = ai_training_dir / "training_data_registry.json"
        
        await agent3.initialize(incremental=True)
        result3 = await agent3.start(incremental=True)
        
        logger.info(f"[TEST 3] Result: {result3}")
        
        # Check final registry state
        if agent3.training_registry_path.exists():
            with open(agent3.training_registry_path, 'r') as f:
                final_registry = json.load(f)
            logger.info(f"[TEST 3] Final registry has {len(final_registry.get('processed_data_hashes', {}))} processed data hashes")
            logger.info(f"[TEST 3] Total records processed: {final_registry.get('data_statistics', {}).get('total_records_processed', 0)}")
        
        await agent3.cleanup()
        
        logger.info("[TEST] All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"[TEST] Test failed: {e}")
        return False
    
    finally:
        # Cleanup test directory
        try:
            shutil.rmtree(test_dir)
            logger.info(f"[TEST] Cleaned up temporary directory: {test_dir}")
        except Exception as e:
            logger.warning(f"[TEST] Failed to cleanup temporary directory: {e}")

def create_sample_training_data(rows: int = 1000) -> pl.DataFrame:
    """Create sample training data for testing"""
    np.random.seed(42)
    
    data = {
        'timestamp': [datetime.now().isoformat() for _ in range(rows)],
        'symbol': [f'NIFTY24DEC{15000 + i % 1000}PE' for i in range(rows)],
        'option_type': ['PE' if i % 2 == 0 else 'CE' for i in range(rows)],
        'strike_price': [15000 + i % 1000 for i in range(rows)],
        'close': np.random.uniform(10, 500, rows),
        'volume': np.random.randint(100, 10000, rows),
        'open_interest': np.random.randint(1000, 100000, rows),
        
        # Greeks
        'delta': np.random.uniform(-1, 1, rows),
        'gamma': np.random.uniform(0, 0.1, rows),
        'theta': np.random.uniform(-10, 0, rows),
        'vega': np.random.uniform(0, 50, rows),
        'iv_rank': np.random.uniform(0, 100, rows),
        
        # Index features
        'index_price': np.random.uniform(23000, 25000, rows),
        'index_return_1min': np.random.uniform(-0.02, 0.02, rows),
        'index_return_3min': np.random.uniform(-0.05, 0.05, rows),
        'index_return_5min': np.random.uniform(-0.08, 0.08, rows),
        'index_return_15min': np.random.uniform(-0.15, 0.15, rows),
        'index_volatility': np.random.uniform(10, 30, rows),
        'index_volatility_10': np.random.uniform(10, 30, rows),
        'index_volatility_50': np.random.uniform(10, 30, rows),
        'index_momentum_10': np.random.uniform(-5, 5, rows),
        'index_momentum_20': np.random.uniform(-10, 10, rows),
        'index_above_sma20': np.random.choice([True, False], rows),
        'index_above_sma50': np.random.choice([True, False], rows),
        
        # Options-specific features
        'moneyness': np.random.uniform(0.8, 1.2, rows),
        'moneyness_deviation': np.random.uniform(-0.2, 0.2, rows),
        'distance_from_atm': np.random.uniform(0, 1000, rows),
        'is_itm': np.random.choice([True, False], rows),
        'time_to_expiry': np.random.uniform(1, 30, rows),
        
        # Combined features
        'delta_exposure': np.random.uniform(-1000, 1000, rows),
        'delta_pnl_1min': np.random.uniform(-100, 100, rows),
        'gamma_risk': np.random.uniform(0, 1000, rows),
        'vega_exposure': np.random.uniform(-500, 500, rows),
        'theta_decay_rate': np.random.uniform(-50, 0, rows),
        
        # PE/CE analysis
        'is_pe': [option_type == 'PE' for option_type in ['PE' if i % 2 == 0 else 'CE' for i in range(rows)]],
        'is_ce': [option_type == 'CE' for option_type in ['PE' if i % 2 == 0 else 'CE' for i in range(rows)]],
        'direction_alignment': np.random.choice([True, False], rows),
        
        # Technical features
        'rsi': np.random.uniform(20, 80, rows),
        'sma_20': np.random.uniform(23000, 25000, rows),
        'ema_20': np.random.uniform(23000, 25000, rows),
        'volume_ratio': np.random.uniform(0.5, 2.0, rows),
        'momentum': np.random.uniform(-5, 5, rows)
    }
    
    return pl.DataFrame(data)

if __name__ == "__main__":
    asyncio.run(test_incremental_training())
