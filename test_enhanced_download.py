#!/usr/bin/env python3
"""
Test script for Enhanced Download Manager with Multiprocessing and Retry Mechanisms
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_enhanced_download_manager():
    """Test the enhanced download manager functionality"""
    
    # Create temporary directory for testing
    test_dir = Path(tempfile.mkdtemp(prefix="test_enhanced_download_"))
    logger.info(f"[TEST] Using temporary directory: {test_dir}")
    
    try:
        # Mock SmartAPI for testing
        class MockSmartAPI:
            def __init__(self):
                self.call_count = 0
                self.fail_rate = 0.2  # 20% failure rate for testing retry mechanism
            
            def getCandleData(self, params):
                self.call_count += 1
                
                # Simulate API rate limiting
                time.sleep(0.1)  # Simulate network delay
                
                # Simulate failures for testing retry mechanism
                if self.call_count % 5 == 0:  # Every 5th call fails
                    return {
                        'status': False,
                        'message': 'Rate limit exceeded',
                        'data': None
                    }
                
                # Simulate timeout errors occasionally
                if self.call_count % 7 == 0:
                    raise Exception("Connection timeout")
                
                # Return mock data
                mock_data = []
                base_time = datetime.now()
                for i in range(10):  # 10 data points per instrument
                    timestamp = (base_time + timedelta(minutes=i)).strftime("%Y-%m-%dT%H:%M:%S+05:30")
                    mock_data.append([
                        timestamp,
                        100.0 + i,  # open
                        105.0 + i,  # high
                        95.0 + i,   # low
                        102.0 + i,  # close
                        1000 + i*10  # volume
                    ])
                
                return {
                    'status': True,
                    'message': 'Success',
                    'data': mock_data
                }
        
        # Import and test the enhanced download manager
        import sys
        sys.path.append(str(Path(__file__).parent))
        
        from agents.enhanced_download_manager import EnhancedDownloadManager, DownloadTask
        
        # Initialize mock API and download manager
        mock_api = MockSmartAPI()
        download_manager = EnhancedDownloadManager(
            smart_api=mock_api,
            max_workers=5,
            max_calls_per_minute=90,
            min_sleep_time=0.1,  # Reduced for testing
            max_retries=2,
            retry_delay=0.5,
            exponential_backoff=True
        )
        
        logger.info("[TEST] Enhanced Download Manager initialized")
        
        # Create test download tasks
        test_tasks = []
        underlyings = ['NIFTY', 'BANKNIFTY']
        option_types = ['CE', 'PE']
        
        task_id = 0
        for underlying in underlyings:
            for strike in [24000, 24050, 24100]:  # 3 strikes per underlying
                for option_type in option_types:
                    task = DownloadTask(
                        task_id=f"test_{task_id}",
                        symbol=f"{underlying}24DEC{strike}{option_type}",
                        token=f"token_{task_id}",
                        exchange="NFO",
                        underlying=underlying,
                        strike_price=float(strike),
                        option_type=option_type,
                        start_date="2024-12-01 09:15",
                        end_date="2024-12-01 15:30",
                        interval="ONE_MINUTE",
                        max_retries=2
                    )
                    test_tasks.append(task)
                    task_id += 1
        
        logger.info(f"[TEST] Created {len(test_tasks)} test download tasks")
        
        # Set up progress tracking
        progress_updates = []
        def progress_callback(completed: int, total: int, failed: int):
            progress_updates.append((completed, total, failed))
            success_rate = ((completed - failed) / completed * 100) if completed > 0 else 0
            logger.info(f"[PROGRESS] {completed}/{total} completed, {failed} failed, {success_rate:.1f}% success")
        
        download_manager.set_progress_callback(progress_callback)
        
        # Add tasks and start download
        download_manager.add_download_tasks(test_tasks)
        
        start_time = time.time()
        logger.info("[TEST] Starting enhanced download test...")
        
        # Execute downloads
        results = await download_manager.download_all_tasks()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Analyze results
        successful_downloads = sum(1 for result in results.values() if result.success)
        failed_downloads = len(results) - successful_downloads
        total_api_calls = mock_api.call_count
        
        logger.info("=" * 60)
        logger.info("[TEST RESULTS]")
        logger.info("=" * 60)
        logger.info(f"Total Tasks: {len(test_tasks)}")
        logger.info(f"Successful Downloads: {successful_downloads}")
        logger.info(f"Failed Downloads: {failed_downloads}")
        logger.info(f"Success Rate: {(successful_downloads / len(test_tasks) * 100):.1f}%")
        logger.info(f"Total Duration: {duration:.2f}s")
        logger.info(f"Total API Calls: {total_api_calls}")
        logger.info(f"API Calls per Second: {(total_api_calls / duration):.2f}")
        logger.info(f"Progress Updates: {len(progress_updates)}")
        logger.info("=" * 60)
        
        # Test retry mechanism
        failed_tasks = download_manager.get_failed_tasks()
        retry_count = sum(task.retry_count for task in failed_tasks)
        logger.info(f"[RETRY] Total retry attempts: {retry_count}")
        
        # Test statistics
        stats = download_manager.get_statistics()
        logger.info(f"[STATS] Completed: {stats.completed_tasks}, Failed: {stats.failed_tasks}")
        logger.info(f"[STATS] Retried: {stats.retried_tasks}, Total API calls: {stats.total_api_calls}")
        
        # Test file saving (mock)
        output_path = test_dir / "output"
        if successful_downloads > 0:
            saved_files = download_manager.save_results_to_files(output_path)
            logger.info(f"[FILES] Saved {len(saved_files)} result files")
        
        # Test failed tasks report
        if failed_downloads > 0:
            report_file = download_manager.save_failed_tasks_report(test_dir)
            logger.info(f"[REPORT] Failed tasks report saved to {report_file}")
        
        # Verify multiprocessing benefits
        expected_sequential_time = len(test_tasks) * 0.5  # Assume 0.5s per task sequentially
        speedup = expected_sequential_time / duration if duration > 0 else 1
        logger.info(f"[PERFORMANCE] Estimated speedup: {speedup:.2f}x")
        
        # Test rate limiting compliance
        calls_per_minute = (total_api_calls / (duration / 60)) if duration > 0 else 0
        logger.info(f"[RATE LIMIT] Actual calls per minute: {calls_per_minute:.1f} (limit: 90)")
        
        if calls_per_minute <= 90:
            logger.info("[RATE LIMIT] ✅ Rate limiting working correctly")
        else:
            logger.warning("[RATE LIMIT] ⚠️ Rate limit may have been exceeded")
        
        # Verify retry mechanism
        if retry_count > 0:
            logger.info("[RETRY] ✅ Retry mechanism working correctly")
        else:
            logger.info("[RETRY] ℹ️ No retries needed in this test")
        
        # Overall test result
        if successful_downloads > 0 and calls_per_minute <= 90:
            logger.info("[TEST] ✅ Enhanced download manager test PASSED")
            return True
        else:
            logger.error("[TEST] ❌ Enhanced download manager test FAILED")
            return False
        
    except Exception as e:
        logger.error(f"[TEST] Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup test directory
        try:
            shutil.rmtree(test_dir)
            logger.info(f"[TEST] Cleaned up temporary directory: {test_dir}")
        except Exception as e:
            logger.warning(f"[TEST] Failed to cleanup temporary directory: {e}")

async def test_rate_limiting():
    """Test rate limiting functionality specifically"""
    logger.info("[RATE TEST] Testing rate limiting functionality...")
    
    from agents.enhanced_download_manager import RateLimiter
    
    # Test rate limiter with aggressive limits
    rate_limiter = RateLimiter(max_calls_per_minute=10, min_sleep_time=0.1)
    
    start_time = time.time()
    call_times = []
    
    # Make 15 calls (should trigger rate limiting)
    for i in range(15):
        call_start = time.time()
        rate_limiter.wait_if_needed()
        call_times.append(time.time() - call_start)
        logger.debug(f"[RATE TEST] Call {i+1} took {call_times[-1]:.3f}s")
    
    total_time = time.time() - start_time
    calls_per_minute = (15 / (total_time / 60))
    
    logger.info(f"[RATE TEST] 15 calls completed in {total_time:.2f}s")
    logger.info(f"[RATE TEST] Effective rate: {calls_per_minute:.1f} calls/minute")
    logger.info(f"[RATE TEST] Average call delay: {sum(call_times)/len(call_times):.3f}s")
    
    if calls_per_minute <= 10:
        logger.info("[RATE TEST] ✅ Rate limiting test PASSED")
        return True
    else:
        logger.error("[RATE TEST] ❌ Rate limiting test FAILED")
        return False

if __name__ == "__main__":
    async def run_all_tests():
        logger.info("Starting Enhanced Download Manager Tests...")
        
        # Test 1: Main functionality
        test1_result = await test_enhanced_download_manager()
        
        # Test 2: Rate limiting
        test2_result = await test_rate_limiting()
        
        # Overall result
        if test1_result and test2_result:
            logger.info("🎉 All tests PASSED!")
        else:
            logger.error("❌ Some tests FAILED!")
    
    asyncio.run(run_all_tests())
