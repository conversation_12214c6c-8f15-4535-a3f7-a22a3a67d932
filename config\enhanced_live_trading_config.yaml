# Enhanced Live Trading Configuration
# Supports Demo, Paper, and Real Trading Modes

# Trading Mode Configuration
trading_mode:
  default: "demo"  # demo, paper, real
  paper_trading:
    initial_balance: 100000.0  # Rs. 1,00,000
    save_interval: 300  # Save account state every 5 minutes
    data_path: "data/paper_trading"
  real_trading:
    api_validation: true
    risk_checks: true
    max_daily_loss: 10000.0  # Rs. 10,000 max daily loss
    position_limits:
      max_positions: 10
      max_position_size: 50000.0  # Rs. 50,000 per position

# SmartAPI Configuration
smartapi:
  websocket:
    reconnect_attempts: 5
    reconnect_delay: 5  # seconds
    heartbeat_interval: 30  # seconds
    subscription_mode: "FULL"  # FULL, QUOTE, LTP
  data_processing:
    buffer_size: 1000  # Number of ticks to keep in buffer
    processing_interval: 0.1  # Process data every 100ms
    anomaly_detection: true

# Market Monitoring Configuration
market_monitoring:
  timeframes: ["1min", "3min", "5min", "15min"]
  monitoring_intervals:
    1min: 60    # Monitor every 60 seconds
    3min: 180   # Monitor every 3 minutes
    5min: 300   # Monitor every 5 minutes
    15min: 900  # Monitor every 15 minutes
  
  # Real-time data sources by mode
  data_sources:
    demo:
      type: "simulated"
      file_path: "data/demo/market_data.parquet"
    paper:
      type: "smartapi_websocket"
      fallback: "historical"
    real:
      type: "smartapi_websocket"
      fallback: "none"
  
  # Alert thresholds
  alerts:
    price_change_threshold: 2.0  # 2% price change
    volume_spike_threshold: 3.0  # 3x average volume
    volatility_threshold: 25.0   # 25% implied volatility
    oi_change_threshold: 20.0    # 20% OI change
  
  # Option chain monitoring
  option_chain:
    symbols: ["NIFTY", "BANKNIFTY"]
    strike_range: 10  # Monitor 10 strikes on each side
    expiry_count: 3   # Monitor 3 nearest expiries
    update_frequency: 5  # Update every 5 seconds

# Signal Generation Configuration
signal_generation:
  models:
    demo:
      type: "rule_based"
      config_path: "config/demo_signals.yaml"
    paper:
      type: "ml_enhanced"
      model_path: "models/options_signal_model.pkl"
      confidence_threshold: 0.55  # Reduced from 0.7 to 0.55 for paper trading
    real:
      type: "ml_enhanced"
      model_path: "models/options_signal_model.pkl"
      confidence_threshold: 0.60  # Reduced from 0.8 to 0.60 for real trading (still conservative)
  
  # Signal processing
  processing:
    multi_timeframe: true
    signal_aggregation: "weighted_average"
    min_signals_required: 2
    signal_timeout: 300  # 5 minutes

# Risk Management Configuration
risk_management:
  position_sizing:
    method: "fixed_percentage"  # fixed_percentage, kelly, volatility_adjusted
    risk_per_trade: 0.02  # 2% of capital per trade
    max_portfolio_risk: 0.10  # 10% max portfolio risk
  
  # Greeks limits
  greeks_limits:
    max_delta: 100
    max_gamma: 50
    max_theta: -500
    max_vega: 1000
  
  # Stop loss configuration
  stop_loss:
    enabled: true
    type: "percentage"  # percentage, absolute, trailing
    percentage: 0.20  # 20% stop loss
    trailing_percentage: 0.15  # 15% trailing stop
  
  # Risk monitoring
  monitoring:
    real_time: true
    alert_thresholds:
      portfolio_loss: 0.05  # 5% portfolio loss alert
      position_loss: 0.10   # 10% position loss alert
      margin_utilization: 0.80  # 80% margin utilization alert

# Execution Configuration
execution:
  order_management:
    demo:
      execution_delay: 0  # Instant execution
      slippage: 0.0
    paper:
      execution_delay: 1  # 1 second delay
      slippage: 0.001  # 0.1% slippage
    real:
      execution_delay: 0  # Real-time execution
      order_timeout: 30  # 30 seconds order timeout
  
  # Execution algorithms
  algorithms:
    default: "market"  # market, limit, twap, vwap
    twap:
      duration: 300  # 5 minutes
      slice_count: 10
    vwap:
      participation_rate: 0.1  # 10% of volume
      max_duration: 600  # 10 minutes
  
  # Order validation
  validation:
    enabled: true
    checks:
      - "margin_available"
      - "position_limits"
      - "risk_limits"
      - "market_hours"

# Performance Analysis Configuration
performance_analysis:
  metrics:
    real_time:
      - "current_pnl"
      - "portfolio_delta"
      - "portfolio_gamma"
      - "portfolio_theta"
      - "portfolio_vega"
    daily:
      - "daily_pnl"
      - "sharpe_ratio"
      - "max_drawdown"
      - "win_rate"
      - "profit_factor"
  
  # Reporting
  reporting:
    frequency: "real_time"  # real_time, hourly, daily
    formats: ["json", "csv"]
    storage_path: "data/performance"
  
  # Benchmarking
  benchmarks:
    - "NIFTY"
    - "BANKNIFTY"
    - "risk_free_rate"

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # File logging
  files:
    main: "logs/enhanced_main.log"
    market_monitoring: "logs/market_monitoring.log"
    signal_generation: "logs/signal_generation.log"
    risk_management: "logs/risk_management.log"
    execution: "logs/execution.log"
    performance: "logs/performance.log"
  
  # Log rotation
  rotation:
    max_size: "10MB"
    backup_count: 5

# Data Storage Configuration
data_storage:
  paths:
    live_data: "data/live"
    historical_data: "data/historical"
    paper_trading: "data/paper_trading"
    real_trading: "data/real_trading"
    models: "models"
    configs: "config"
  
  # Data retention
  retention:
    tick_data: 7  # days
    minute_data: 30  # days
    daily_data: 365  # days
    trade_data: 1095  # 3 years
  
  # Backup configuration
  backup:
    enabled: true
    frequency: "daily"
    storage_path: "backups"
    compression: true

# System Configuration
system:
  # Resource limits
  resources:
    max_memory_usage: "2GB"
    max_cpu_usage: 80  # percentage
    max_disk_usage: "10GB"
  
  # Health monitoring
  health_checks:
    enabled: true
    interval: 60  # seconds
    alerts:
      memory_threshold: 0.8
      cpu_threshold: 0.9
      disk_threshold: 0.9
  
  # Failover configuration
  failover:
    enabled: true
    backup_systems: []
    auto_restart: true
    max_restart_attempts: 3