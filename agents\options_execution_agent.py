#!/usr/bin/env python3
"""
Options Execution Agent - Smart Options Order Execution

Features:
🚀 1. Smart Order Execution
- Multi-leg strategy execution
- Optimal order timing
- Slippage minimization
- Market impact reduction

📊 2. Order Management
- Order lifecycle management
- Partial fill handling
- Order modification and cancellation
- Execution quality monitoring

⚡ 3. SmartAPI Integration
- NFO segment order placement
- Real-time order status
- Margin calculation
- Position management

🎯 4. Execution Algorithms
- TWAP (Time Weighted Average Price)
- VWAP (Volume Weighted Average Price)
- Implementation Shortfall
- Market-on-Close execution
"""

import asyncio
import logging
import polars as pl
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import os # Import os to access environment variables
from dotenv import load_dotenv # Import load_dotenv
from SmartApi import SmartConnect, SmartWebSocket # SmartAPI imports
from agents.options_risk_management_agent import OptionsRiskManagementAgent # Import Risk Management Agent
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent # Import Market Monitoring Agent
from datetime import timedelta # Import timedelta for EOD square-off

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

class OptionsExecutionAgent:
    """Options Execution Agent for smart order execution"""
    
    def __init__(self, config_path: str = "config/options_execution_config.yaml", dry_run: bool = True):
        self.config_path = config_path
        self.config = None
        self.is_running = False
        self.smartapi_client: Optional[SmartAPI] = None
        self.active_orders: Dict[str, Any] = {} # To track orders by order_id
        self.dry_run = dry_run # Flag for paper trading mode
        self.risk_agent: Optional[OptionsRiskManagementAgent] = None # Instance of Risk Management Agent
        self.market_data_agent: Optional[OptionsMarketMonitoringAgent] = None # Instance of Market Monitoring Agent
        self.eod_square_off_time = datetime.now().replace(hour=15, minute=20, second=0, microsecond=0) # Example: 3:20 PM for EOD square-off
        self.persistence_file = Path("data/execution/active_orders.parquet") # Path for persisting active orders
        
        logger.info("🚀 [INIT] Options Execution Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent"""
        try:
            await self._load_config()
            if not self.dry_run:
                await self._init_smartapi()
                await self._reconstruct_active_orders() # Recover from incomplete state

            # Initialize Risk Management Agent
            self.risk_agent = OptionsRiskManagementAgent()
            await self.risk_agent.initialize(**kwargs)

            # Initialize Market Monitoring Agent
            self.market_data_agent = OptionsMarketMonitoringAgent()
            await self.market_data_agent.initialize(**kwargs)

            # Load previously active orders if available
            await self._load_active_orders()

            logger.info("✅ [SUCCESS] Options Execution Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration"""
        # In a real scenario, this would load from a YAML file
        self.config = {
            'execution_algorithms': ['market', 'limit', 'twap', 'vwap'],
            'max_slippage': float(os.getenv('MAX_SLIPPAGE', 0.02)),
            'order_timeout': int(os.getenv('ORDER_TIMEOUT', 300)),  # seconds
            'retry_attempts': int(os.getenv('RETRY_ATTEMPTS', 3)),
            'smartapi_api_key': os.getenv('SMARTAPI_API_KEY'),
            'smartapi_client_code': os.getenv('SMARTAPI_USERNAME'),
            'smartapi_password': os.getenv('SMARTAPI_PASSWORD'),
            'smartapi_totp_secret': os.getenv('SMARTAPI_TOTP_TOKEN'), # Optional TOTP
            'banknifty_lot_size': int(os.getenv('BANKNIFTY_LOT_SIZE', 25)) # Example lot size for BANKNIFTY
        }
        # Validate essential SmartAPI credentials
        if not all([self.config['smartapi_api_key'], self.config['smartapi_client_code'], self.config['smartapi_password']]):
            logger.error("❌ Missing one or more SmartAPI credentials in environment variables. Please check .env file.")
            raise ValueError("SmartAPI credentials not fully configured.")
        logger.info("⚙️ Configuration loaded from environment variables.")
    
    async def _init_smartapi(self):
        """Initialize and authenticate with Angel One SmartAPI"""
        try:
            self.smartapi_client = SmartAPI(api_key=self.config['smartapi_api_key'])
            
            # Assuming a simple login flow for demonstration.
            # In production, you'd handle session management, refresh tokens, etc.
            data = self.smartapi_client.generateSession(
                self.config['smartapi_client_code'],
                self.config['smartapi_password'],
                self.config.get('smartapi_totp_secret') # Optional TOTP
            )
            
            if data and data.get('status'):
                logger.info("🔑 SmartAPI session generated successfully.")
                # Set access token and refresh token
                self.smartapi_client.setAccessToken(data['data']['jwtToken'])
                self.smartapi_client.setRefreshToken(data['data']['refreshToken'])
                # Store tokens for manual refresh if needed
                self.access_token = data['data']['jwtToken']
                self.refresh_token = data['data']['refreshToken']
            else:
                raise Exception(f"SmartAPI session generation failed: {data.get('message', 'Unknown error')}")
        except Exception as e:
            logger.error(f"❌ [ERROR] SmartAPI initialization failed: {e}")
            raise

    def _resolve_option_symbol(self, underlying: str, strike_price: int, option_type: str, expiry: str) -> str:
        """
        Resolves the option symbol for SmartAPI.
        Example: BANKNIFTY, 47800, CE, 2025-07-25 -> BANKNIFTY25JUL47800CE
        """
        try:
            # Format expiry date: YYYY-MM-DD to DDMMMYY (e.g., 2025-07-25 -> 25JUL25)
            expiry_dt = datetime.strptime(expiry, "%Y-%m-%d")
            # Angel One uses a specific format for month (e.g., JUL for July)
            # For year, it's usually last two digits.
            # This might need adjustment based on actual SmartAPI symbol conventions.
            formatted_expiry = expiry_dt.strftime("%d%b%y").upper()
            
            # Construct the trading symbol
            # Example: BANKNIFTY25JUL47800CE
            trading_symbol = f"{underlying}{formatted_expiry}{strike_price}{option_type}"
            logger.info(f"🔍 Resolved option symbol: {trading_symbol}")
            return trading_symbol
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to resolve option symbol for {underlying} {strike_price} {option_type} {expiry}: {e}")
            raise

    async def place_order(self, order_params: Dict[str, Any]) -> Optional[str]:
        """
        Places an order via Angel One SmartAPI.
        Automatically selects Exchange (NFO), Product type (MIS, NRML), Order type (LIMIT, MARKET).
        """
        if self.dry_run:
            order_id = f"DRYRUN_{datetime.now().timestamp()}_{order_params.get('tradingsymbol', 'UNKNOWN')}"
            logger.info(f"📝 [DRY RUN] Placing order: {order_params} -> Order ID: {order_id}")
            self.active_orders[order_id] = {
                "order_id": order_id,
                "status": "QUEUED",
                "filled_qty": 0,
                "avg_price": 0,
                "signal_data": order_params,
                "entry_time": datetime.now().isoformat(),
                "last_update_time": datetime.now().isoformat(),
                "transaction_type": order_params.get('transaction_type'),
                "tradingsymbol": order_params.get('tradingsymbol'),
                "quantity": order_params.get('quantity'),
                "price": order_params.get('price'),
                "order_type": order_params.get('order_type'),
                "product": order_params.get('product'),
                "exchange": order_params.get('exchange'),
                "variety": order_params.get('variety')
            }
            await self._save_active_orders() # Save after placing order
            return order_id

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot place order.")
            return None

        try:
            # Default values for Angel One SmartAPI
            order_data = {
                "exchange": "NFO", # Options are typically NFO
                "variety": order_params.get('variety', 'NORMAL'), # NORMAL, AMO, CO, BO
                "tradingsymbol": order_params['tradingsymbol'],
                "transaction_type": order_params['transaction_type'], # BUY/SELL
                "quantity": order_params['quantity'],
                "product": order_params.get('product', 'MIS'), # MIS (Intraday), NRML (Delivery)
                "order_type": order_params.get('order_type', 'LIMIT'), # LIMIT, MARKET
                "price": order_params.get('price', 0), # Required for LIMIT orders
                "triggerprice": order_params.get('triggerprice', 0), # Required for SL/SL-M orders
                "squareoff": order_params.get('squareoff', 0), # For BO/CO
                "stoploss": order_params.get('stoploss', 0), # For BO/CO
                "trailingStoploss": order_params.get('trailingStoploss', 0), # For BO
                "disclosedquantity": order_params.get('disclosedquantity', 0),
                "ordertag": order_params.get('ordertag', ''),
                "duration": order_params.get('duration', 'DAY') # Added missing duration parameter
            }

            response = self.smartapi_client.placeOrder(order_data)
            if response and response.get('status'):
                order_id = response['data']['orderid']
                logger.info(f"✅ Order placed successfully! Order ID: {order_id}, Details: {order_data}")
                self.active_orders[order_id] = {
                    "order_id": order_id,
                    "status": "QUEUED", # Initial status
                    "filled_qty": 0,
                    "avg_price": 0,
                    "signal_data": order_params, # Store original signal for tracking
                    "entry_time": datetime.now().isoformat(),
                    "last_update_time": datetime.now().isoformat(),
                    **order_data # Store all order data
                }
                self._log_trade_event(order_id, "QUEUED", order_data) # Log order queued event
                await self._save_active_orders() # Save after placing order
                return order_id
            else:
                logger.error(f"❌ Failed to place order: {response.get('message', 'Unknown error')}, Details: {order_data}")
                self._log_trade_event("N/A", "PLACEMENT_FAILED", {"reason": response.get('message', 'Unknown error'), "order_params": order_data})
                return None
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while placing order: {e}, Order Params: {order_params}")
            return None

    async def modify_order(self, order_id: str, new_price: Optional[float] = None, new_quantity: Optional[int] = None) -> bool:
        """Modifies an existing order."""
        if self.dry_run:
            logger.info(f"📝 [DRY RUN] Modifying order {order_id}: new_price={new_price}, new_quantity={new_quantity}")
            if order_id in self.active_orders:
                if new_price is not None:
                    self.active_orders[order_id]['price'] = new_price
                if new_quantity is not None:
                    self.active_orders[order_id]['quantity'] = new_quantity
                self.active_orders[order_id]['last_update_time'] = datetime.now().isoformat()
                logger.info(f"📝 [DRY RUN] Order {order_id} modified.")
                await self._save_active_orders() # Save after modifying order
                return True
            logger.warning(f"⚠️ [DRY RUN] Order {order_id} not found for modification.")
            return False

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot modify order.")
            return False

        if order_id not in self.active_orders:
            logger.warning(f"⚠️ Order {order_id} not found in active orders. Cannot modify.")
            return False

        current_order = self.active_orders[order_id]
        modify_data = {
            "variety": current_order['variety'],
            "orderid": order_id,
            "exchange": current_order['exchange'],
            "tradingsymbol": current_order['tradingsymbol'],
            "symboltoken": current_order.get('symboltoken', ''), # Added missing symboltoken parameter
            "ordertype": current_order['order_type'],
            "producttype": current_order['product'],
            "duration": "DAY", # Or GTC, IOC etc.
            "price": new_price if new_price is not None else current_order['price'],
            "quantity": new_quantity if new_quantity is not None else current_order['quantity'],
            "triggerprice": current_order.get('triggerprice', 0)
        }

        try:
            response = self.smartapi_client.modifyOrder(modify_data)
            if response and response.get('status'):
                logger.info(f"✅ Order {order_id} modified successfully.")
                self.active_orders[order_id]['price'] = modify_data['price']
                self.active_orders[order_id]['quantity'] = modify_data['quantity']
                self.active_orders[order_id]['last_update_time'] = datetime.now().isoformat()
                await self._save_active_orders() # Save after modifying order
                return True
            else:
                logger.error(f"❌ Failed to modify order {order_id}: {response.get('message', 'Unknown error')}")
                return False
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while modifying order {order_id}: {e}")
            return False

    async def cancel_order(self, order_id: str) -> bool:
        """Cancels an existing order."""
        if self.dry_run:
            logger.info(f"📝 [DRY RUN] Cancelling order {order_id}")
            if order_id in self.active_orders:
                self.active_orders[order_id]['status'] = "CANCELLED"
                self.active_orders[order_id]['last_update_time'] = datetime.now().isoformat()
                logger.info(f"📝 [DRY RUN] Order {order_id} cancelled.")
                await self._save_active_orders() # Save after cancelling order
                return True
            logger.warning(f"⚠️ [DRY RUN] Order {order_id} not found for cancellation.")
            return False

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot cancel order.")
            return False

        if order_id not in self.active_orders:
            logger.warning(f"⚠️ Order {order_id} not found in active orders. Cannot cancel.")
            return False

        current_order = self.active_orders[order_id]
        cancel_data = {
            "variety": current_order['variety'],
            "orderid": order_id,
            "exchange": current_order['exchange'],
            "tradingsymbol": current_order['tradingsymbol']
        }

        try:
            response = self.smartapi_client.cancelOrder(cancel_data)
            if response and response.get('status'):
                logger.info(f"✅ Order {order_id} cancelled successfully.")
                self.active_orders[order_id]['status'] = "CANCELLED"
                self.active_orders[order_id]['last_update_time'] = datetime.now().isoformat()
                await self._save_active_orders() # Save after cancelling order
                return True
            else:
                logger.error(f"❌ Failed to cancel order {order_id}: {response.get('message', 'Unknown error')}")
                return False
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while cancelling order {order_id}: {e}")
            return False

    async def get_order_book(self) -> List[Dict[str, Any]]:
        """Retrieves the current order book from SmartAPI."""
        if self.dry_run:
            logger.info("📝 [DRY RUN] Getting order book.")
            # Simulate order book for dry run
            return list(self.active_orders.values())

        if not self.smartapi_client:
            logger.error("❌ SmartAPI client not initialized. Cannot get order book.")
            return []

        try:
            response = self.smartapi_client.getOrderBook()
            if response and response.get('status'):
                logger.info("📚 Successfully retrieved order book.")
                return response['data']
            else:
                logger.error(f"❌ Failed to get order book: {response.get('message', 'Unknown error')}")
                return []
        except Exception as e:
            logger.error(f"❌ [ERROR] Exception while getting order book: {e}")
            return []

    async def _process_signal(self, signal: Dict[str, Any]) -> Optional[str]:
        """
        Receives structured trade signal from Signal Generation Agent and converts it
        into a SmartAPI-ready order object, then places the order.
        """
        logger.info(f"⚡ Processing signal: {signal}")
        try:
            # 1. Execution Risk Checks
            if self.risk_agent:
                # Check capital at risk, daily limits, duplicate signals, over-positioning
                # This is a simplified call; actual risk agent would have more detailed checks
                risk_check_passed, reason = await self.risk_agent.check_trade_risk(signal)
                if not risk_check_passed:
                    logger.warning(f"⚠️ Risk check failed for signal {signal.get('strategy_id', 'N/A')}: {reason}. Aborting order placement.")
                    self._send_notification(f"🚫 Order Aborted: Risk check failed for {signal.get('underlying')}. Reason: {reason}")
                    return None

            # 2. Resolve option symbol
            trading_symbol = self._resolve_option_symbol(
                underlying=signal['underlying'],
                strike_price=signal['strike_price'],
                option_type=signal['option_type'],
                expiry=signal['expiry']
            )

            # 3. Determine transaction type
            transaction_type = "BUY" if signal['action'].upper() == "BUY" else "SELL"

            # 4. Determine product type (MIS for intraday, NRML for positional)
            # For simplicity, assuming MIS for now. This could be configurable.
            product_type = "MIS" 

            # 5. Determine order type (LIMIT or MARKET)
            # If entry_price is provided and not 0, use LIMIT. Otherwise, MARKET.
            order_type = "LIMIT" if signal.get('entry_price') else "MARKET"
            price = signal.get('entry_price', 0)

            # 6. Calculate quantity based on lot_size and underlying's lot size
            # Assuming lot_size in signal is number of lots, not total quantity
            quantity = signal['lot_size'] * self.config['banknifty_lot_size'] # Example for BANKNIFTY

            # 7. Slippage check for MARKET orders (optional, requires real-time LTP)
            if order_type == "MARKET" and signal.get('entry_price'):
                current_ltp = await self.market_data_agent.get_ltp(trading_symbol) # Fetch real-time LTP
                if current_ltp:
                    slippage_abs = abs(current_ltp - signal['entry_price'])
                    slippage_pct = (slippage_abs / signal['entry_price']) if signal['entry_price'] else 0
                    if slippage_pct > self.config['max_slippage']:
                        logger.warning(f"⚠️ Potential slippage {slippage_pct:.2%} exceeds max allowed {self.config['max_slippage']:.2%}. Aborting market order for {trading_symbol}.")
                        self._send_notification(f"🚫 Order Aborted: High slippage for {trading_symbol}. Signal Price: {signal['entry_price']}, Current LTP: {current_ltp}.")
                        return None
                else:
                    logger.warning(f"⚠️ Could not fetch real-time LTP for slippage check for {trading_symbol}. Proceeding without slippage abort.")

            order_params = {
                "tradingsymbol": trading_symbol,
                "transaction_type": transaction_type,
                "exchange": "NFO",
                "order_type": order_type,
                "quantity": quantity,
                "price": price,
                "product": product_type,
                "variety": "NORMAL" # Default variety
            }
            
            # 8. Place the order
            order_id = await self.place_order(order_params)
            if order_id:
                logger.info(f"✅ Signal translated and order placed. Order ID: {order_id}")
                # Store the original signal data with the order for later tracking
                self.active_orders[order_id]['original_signal'] = signal
                await self._save_active_orders() # Save after updating active_orders
                self._send_notification(f"✅ Order Placed: {transaction_type} {quantity} of {trading_symbol} @ {price} (Strategy: {signal.get('strategy_id')})")
                return order_id
            else:
                logger.error(f"❌ Failed to place order for signal: {signal}")
                self._send_notification(f"❌ Order Failed: Could not place order for {trading_symbol} (Strategy: {signal.get('strategy_id')})")
                return None

        except Exception as e:
            logger.error(f"❌ [ERROR] Error processing signal {signal}: {e}")
            self._send_notification(f"❌ Order Error: Exception processing signal for {signal.get('underlying')}: {e}")
            return None

    async def _monitor_signals(self):
        """Monitor trading signals for execution"""
        while self.is_running:
            try:
                # This is a placeholder. In a real system, this would receive signals
                # from a message queue, a file, or another agent.
                # For demonstration, let's simulate a signal.
                sample_signal = {
                    "underlying": "BANKNIFTY",
                    "strike_price": 47800,
                    "option_type": "CE",
                    "expiry": "2025-07-25",
                    "action": "BUY",
                    "entry_price": 142,
                    "stoploss": 122,
                    "target": 185,
                    "lot_size": 2,
                    "strategy_id": "strat_012",
                    "confidence": 0.83,
                    "timestamp": datetime.now().isoformat() # Add timestamp for latency calculation
                }
                logger.info("👀 Monitoring trading signals...")
                # await self._process_signal(sample_signal) # Uncomment to test signal processing
                await asyncio.sleep(5)  # Check every 5 seconds
            except Exception as e:
                logger.error(f"❌ [ERROR] Signal monitoring failed: {e}")

    async def _manage_orders(self):
        """Manage active orders: update status, handle partial fills, slippage fallback"""
        while self.is_running:
            try:
                logger.info("🔄 Managing active orders...")
                if not self.active_orders:
                    logger.info("No active orders to manage.")
                    await asyncio.sleep(10)
                    continue

                order_book = await self.get_order_book()
                order_book_map = {order['orderid']: order for order in order_book}

                for order_id, local_order in list(self.active_orders.items()):
                    if local_order['status'] in ["COMPLETE", "CANCELLED", "REJECTED", "EXITED_SL", "EXITED_TP"]:
                        continue # Already finalized

                    smartapi_order = order_book_map.get(order_id)
                    if smartapi_order:
                        # Update status, filled_qty, avg_price
                        new_status = smartapi_order.get('status', local_order['status'])
                        filled_qty = smartapi_order.get('filledquantity', 0)
                        avg_price = smartapi_order.get('averageprice', 0)

                        # Log status changes
                        if new_status != local_order['status']:
                            logger.info(f"📊 Order {order_id} status changed: {local_order['status']} -> {new_status}")
                            local_order['status'] = new_status
                            local_order['last_update_time'] = datetime.now().isoformat()
                            self._log_trade_event(order_id, new_status, smartapi_order) # Log the status change
                            await self._save_active_orders() # Save after status change

                        if filled_qty > local_order['filled_qty']:
                            logger.info(f"📈 Order {order_id} partial fill: {filled_qty} / {local_order['quantity']} @ {avg_price}")
                            local_order['filled_qty'] = filled_qty
                            local_order['avg_price'] = avg_price
                            local_order['last_update_time'] = datetime.now().isoformat()
                            self._log_trade_event(order_id, "PARTIAL_FILL", smartapi_order) # Log partial fill
                            await self._save_active_orders() # Save after partial fill
                        
                        # Handle slippage fallback (e.g., switch from limit to market after X seconds)
                        if local_order['order_type'] == 'LIMIT' and local_order['status'] == 'OPEN':
                            entry_time = datetime.fromisoformat(local_order['entry_time'])
                            time_in_force = (datetime.now() - entry_time).total_seconds()
                            if time_in_force > self.config['order_timeout']:
                                logger.warning(f"⏰ Order {order_id} timed out. Attempting to modify to MARKET order.")
                                # This is a simplified fallback. In reality, you might check LTP first.
                                success = await self.modify_order(order_id, new_price=0, new_quantity=local_order['quantity']) # Price 0 for market order
                                if success:
                                    local_order['order_type'] = 'MARKET'
                                    logger.info(f"✅ Order {order_id} modified to MARKET order.")
                                    self._log_trade_event(order_id, "MODIFIED_TO_MARKET", {"reason": "timeout"})
                                    await self._save_active_orders() # Save after modification
                                else:
                                    logger.error(f"❌ Failed to modify order {order_id} to MARKET. Cancelling.")
                                    await self.cancel_order(order_id)
                                    local_order['status'] = "REJECTED" # Or a specific 'FAILED_FALLBACK' status
                                    self._log_trade_event(order_id, "MODIFICATION_FAILED_CANCELED", {"reason": "failed to modify to market"})
                                    await self._save_active_orders() # Save after cancellation

                        # Handle re-attempts on failure (max retry logic)
                        if (new_status == 'REJECTED' or new_status == 'CANCELLED') and local_order.get('retries', 0) < self.config['retry_attempts']:
                            local_order['retries'] = local_order.get('retries', 0) + 1
                            logger.warning(f"⚠️ Order {order_id} {new_status}. Retrying ({local_order['retries']}/{self.config['retry_attempts']}). Reason: {smartapi_order.get('text', 'N/A')}")
                            # Re-place the order with original parameters
                            # Note: This might need more sophisticated logic (e.g., price adjustment)
                            await self.place_order(local_order['signal_data'])
                            self._send_notification(f"🔄 Order Retry: {order_id} {new_status}. Retrying...")
                            self._log_trade_event(order_id, "RETRYING", {"attempt": local_order['retries'], "reason": smartapi_order.get('text', 'N/A')})
                            await self._save_active_orders() # Save after retry attempt
                        elif new_status == 'REJECTED' or new_status == 'CANCELLED':
                            logger.error(f"❌ Order {order_id} {new_status} after {local_order.get('retries', 0)} retries. Final status.")
                            self._send_notification(f"❌ Order Failed: {order_id} {new_status} after max retries.")
                            self._log_trade_event(order_id, "FAILED_MAX_RETRIES", smartapi_order)
                            local_order['status'] = new_status # Mark as final
                            await self._save_active_orders() # Save final status
                        # No need for explicit elif blocks for COMPLETE, OPEN, PARTIALLY_FILLED as the initial `if new_status != local_order['status']` handles logging.

                    else:
                        # Order not found in SmartAPI order book.
                        # This could mean it was completed/cancelled externally or an API issue.
                        # If it's not already finalized, try to fetch its individual status.
                        if local_order['status'] not in ["COMPLETE", "CANCELLED", "REJECTED", "UNKNOWN_EXTERNAL_STATUS", "EXITED_SL", "EXITED_TP"]:
                            logger.warning(f"⚠️ Order {order_id} not found in SmartAPI order book. Attempting individual status check.")
                            # SmartAPI doesn't have a direct get_order_status by order_id in this client.
                            # In a real scenario, you'd call a specific API endpoint for single order status.
                            # For now, we'll assume it's completed if not found in order book after some time.
                            if (datetime.now() - datetime.fromisoformat(local_order['last_update_time'])).total_seconds() > 60:
                                logger.warning(f"⚠️ Order {order_id} still not found after 60s. Marking as UNKNOWN_EXTERNAL_STATUS.")
                                local_order['status'] = "UNKNOWN_EXTERNAL_STATUS"
                                self._log_trade_event(order_id, "UNKNOWN_STATUS", {"message": "Order not found in order book after timeout."})
                                await self._save_active_orders() # Save unknown status

                await asyncio.sleep(10)  # Check every 10 seconds
            except Exception as e:
                logger.error(f"❌ [ERROR] Order management failed: {e}")

    async def _log_trade_event(self, order_id: str, event_type: str, details: Dict[str, Any]):
        """
        Logs trade events for audit trail and sends to Performance Analysis Agent
        and AI Training Agent.
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "order_id": order_id,
            "event_type": event_type,
            "details": details,
            "local_order_state": self.active_orders.get(order_id, {})
        }
        logger.info(f"📝 TRADE EVENT: {json.dumps(log_entry)}")

        # Placeholder for sending to other agents
        # self.performance_analysis_agent.process_trade_event(log_entry)
        # self.ai_training_agent.feed_live_feedback(log_entry)

    async def _monitor_positions(self):
        """Monitor current positions and track Live Trade P&L"""
        while self.is_running:
            try:
                logger.info("💰 Monitoring positions and P&L...")
                # In a real system, you'd fetch live LTP from a market data feed
                # For now, let's simulate LTP or use a placeholder
                
                # Fetch current positions from SmartAPI (if not dry run)
                positions = []
                if not self.dry_run and self.smartapi_client:
                    response = self.smartapi_client.getPositions()
                    if response and response.get('status'):
                        positions = response['data']['netPositions']
                        logger.info(f"📊 Retrieved {len(positions)} positions from SmartAPI.")
                    else:
                        logger.warning(f"⚠️ Failed to get positions from SmartAPI: {response.get('message', 'N/A')}")
                elif self.dry_run:
                    # Simulate positions from active_orders
                    for order_id, order_data in self.active_orders.items():
                        if order_data['status'] == 'COMPLETE' and order_data['filled_qty'] > 0 and not order_data.get('exit_time'):
                            positions.append({
                                "tradingsymbol": order_data['tradingsymbol'],
                                "netqty": order_data['filled_qty'] if order_data['transaction_type'] == 'BUY' else -order_data['filled_qty'],
                                "buyavgprice": order_data['avg_price'] if order_data['transaction_type'] == 'BUY' else 0,
                                "sellavgprice": order_data['avg_price'] if order_data['transaction_type'] == 'SELL' else 0,
                                "ltp": order_data['avg_price'] * 1.05, # Simulate some profit
                                "pnl": 0 # Will calculate below
                            })

                for pos in positions:
                    trading_symbol = pos.get('tradingsymbol')
                    net_qty = int(pos.get('netqty', 0))
                    buy_avg_price = float(pos.get('buyavgprice', 0))
                    sell_avg_price = float(pos.get('sellavgprice', 0))
                    ltp = float(pos.get('ltp', 0)) # Live Traded Price - needs to come from market data

                    if net_qty == 0:
                        continue # No open position

                    entry_price = buy_avg_price if net_qty > 0 else sell_avg_price
                    
                    # Calculate Unrealized P&L
                    unrealized_pnl = (ltp - entry_price) * abs(net_qty)
                    if net_qty < 0: # Short position
                        unrealized_pnl = -unrealized_pnl

                    # Find the corresponding original signal if available
                    original_signal = None
                    order_id_for_pnl = 'N/A'
                    for order_data in self.active_orders.values():
                        if order_data.get('tradingsymbol') == trading_symbol and order_data.get('status') == 'COMPLETE':
                            original_signal = order_data.get('original_signal')
                            order_id_for_pnl = order_data.get('order_id')
                            break

                    pnl_report = {
                        "order_id": order_id_for_pnl, # Include order_id for tracking
                        "tradingsymbol": trading_symbol,
                        "status": "open",
                        "entry_price": entry_price,
                        "ltp": ltp,
                        "pnl": round(unrealized_pnl, 2),
                        "net_quantity": net_qty,
                        "strategy_id": original_signal.get('strategy_id') if original_signal else "N/A",
                        "confidence": original_signal.get('confidence') if original_signal else "N/A",
                        "stoploss_status": "ACTIVE" if self.active_orders.get(order_id_for_pnl, {}).get('sl_order_id') else "N/A",
                        "target_status": "ACTIVE" if self.active_orders.get(order_id_for_pnl, {}).get('tp_order_id') else "N/A"
                    }
                    logger.info(f"📈 Live P&L: {json.dumps(pnl_report)}")
                    # Publish to UI / Telegram / Dashboard
                    self._send_notification(f"📈 Live P&L for {tradingsymbol}: {pnl_report['pnl']} (LTP: {ltp})", llm_readable=True, pnl_data=pnl_report)

                await asyncio.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"❌ [ERROR] Position monitoring failed: {e}")

    async def _calculate_execution_quality(self):
        """Calculate execution quality metrics: slippage, latency"""
        while self.is_running:
            try:
                logger.info("⏱️ Calculating execution quality...")
                for order_id, order_data in self.active_orders.items():
                    if order_data['status'] == 'COMPLETE' and order_data['filled_qty'] > 0:
                        signal_price = order_data['original_signal'].get('entry_price')
                        executed_price = order_data['avg_price']
                        
                        if signal_price and executed_price:
                            slippage_abs = abs(executed_price - signal_price)
                            slippage_pct = (slippage_abs / signal_price) * 100 if signal_price else 0
                            logger.info(f"📊 Order {order_id} - Signal Price: {signal_price}, Executed Price: {executed_price}, Slippage: {slippage_pct:.2f}%")

                            # Optional: Abort order if slippage exceeds N% (this check would be before placing or during modification)
                            # For already filled orders, this is just for logging/analysis.
                            if slippage_pct > (self.config['max_slippage'] * 100):
                                logger.warning(f"⚠️ High slippage detected for order {order_id}: {slippage_pct:.2f}% (Max allowed: {self.config['max_slippage']*100:.2f}%)")

                        # Calculate latency (signal to execution time)
                        signal_time_str = order_data['original_signal'].get('timestamp') # Assuming signal has a timestamp
                        if signal_time_str:
                            try:
                                signal_time = datetime.fromisoformat(signal_time_str)
                                execution_time = datetime.fromisoformat(order_data['entry_time'])
                                latency_seconds = (execution_time - signal_time).total_seconds()
                                logger.info(f"⏱️ Order {order_id} - Latency (Signal to Execution): {latency_seconds:.2f} seconds")
                            except ValueError:
                                logger.warning(f"⚠️ Could not parse signal timestamp for order {order_id}: {signal_time_str}")
                        else:
                            logger.warning(f"⚠️ No signal timestamp found for order {order_id}.")

                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"❌ [ERROR] Execution quality calculation failed: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("🧹 [CLEANUP] Cleaning up Options Execution Agent...")
            self.is_running = False
            await self._save_active_orders() # Save active orders on cleanup
            if self.smartapi_client:
                # Logout or close session if necessary
                # self.smartapi_client.logout() # SmartAPI doesn't have a direct logout method in this client
                if hasattr(self, 'token_manager'):
                    await self.token_manager.stop_token_refresh_loop()
                logger.info("🔑 SmartAPI client resources cleaned up.")
            logger.info("✅ [SUCCESS] Options Execution Agent cleaned up")
        except Exception as e:
            logger.error(f"❌ [ERROR] Cleanup failed: {e}")

    async def start(self):
        """Starts the execution agent's monitoring loops."""
        self.is_running = True
        logger.info("▶️ Options Execution Agent started. Monitoring signals, orders, and positions...")
        await self._start_background_tasks()

    async def stop(self):
        """Stops the execution agent's monitoring loops."""
        logger.info("🛑 Stopping Options Execution Agent...")
        self.is_running = False
        await self.cleanup()

    async def _start_background_tasks(self):
        """Starts all concurrent background tasks."""
        await asyncio.gather(
            self._monitor_signals(),
            self._manage_orders(),
            self._monitor_positions(),
            self._calculate_execution_quality(),
            self._monitor_sl_tp(), # New: Monitor Stop Loss/Target Profit
            self._eod_square_off() # New: End-of-day square-off
        )

    async def _reconstruct_active_orders(self):
        """
        Recovers from an incomplete state by fetching the order book on restart
        and reconstructing the local shadow order book.
        This is primarily for orders placed *before* the agent started,
        complementing the persistence file.
        """
        logger.info("🔄 Reconstructing active orders from SmartAPI order book...")
        try:
            order_book = await self.get_order_book()
            for order in order_book:
                order_id = order.get('orderid')
                if order_id and order.get('status') not in ["COMPLETE", "CANCELLED", "REJECTED"]:
                    # Only add open/pending orders to active_orders if not already loaded from persistence
                    if order_id not in self.active_orders:
                        self.active_orders[order_id] = {
                            "order_id": order_id,
                            "status": order.get('status', 'UNKNOWN'),
                            "filled_qty": order.get('filledquantity', 0),
                            "avg_price": order.get('averageprice', 0),
                            "signal_data": {}, # Cannot reconstruct original signal from API, relies on persistence
                            "entry_time": datetime.now().isoformat(), # Use current time as a fallback
                            "last_update_time": datetime.now().isoformat(),
                            "transaction_type": order.get('transactiontype'),
                            "tradingsymbol": order.get('tradingsymbol'),
                            "quantity": order.get('quantity'),
                            "price": order.get('price'),
                            "order_type": order.get('ordertype'),
                            "product": order.get('producttype'),
                            "exchange": order.get('exchange'),
                            "variety": order.get('variety'),
                            "retries": 0 # Initialize retries for recovered orders
                        }
                        logger.info(f"✅ Recovered active order from SmartAPI: {order_id} with status {order.get('status')}")
            logger.info(f"📊 Reconstructed {len(self.active_orders)} active orders (combined with persisted).")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to reconstruct active orders from SmartAPI: {e}")

    async def _save_active_orders(self):
        """Saves the current state of active_orders to a Parquet file."""
        try:
            if not self.active_orders:
                if self.persistence_file.exists():
                    self.persistence_file.unlink() # Delete file if no active orders
                    logger.info("🗑️ Deleted active orders persistence file as no active orders remain.")
                return

            # Convert dictionary of orders to a list of dictionaries for Polars DataFrame
            orders_list = []
            for order_id, order_data in self.active_orders.items():
                # Flatten nested dictionaries like 'original_signal' for Polars
                flat_order_data = order_data.copy()
                if 'original_signal' in flat_order_data:
                    for k, v in flat_order_data['original_signal'].items():
                        flat_order_data[f"signal_{k}"] = v
                    del flat_order_data['original_signal']
                orders_list.append(flat_order_data)

            df = pl.DataFrame(orders_list)
            self.persistence_file.parent.mkdir(parents=True, exist_ok=True)
            df.write_parquet(self.persistence_file)
            logger.info(f"💾 Active orders saved to {self.persistence_file}")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to save active orders to persistence file: {e}")

    async def _load_active_orders(self):
        """Loads active_orders from a Parquet file on initialization."""
        try:
            if self.persistence_file.exists():
                df = pl.read_parquet(self.persistence_file)
                loaded_orders = df.to_dicts()
                for order_data in loaded_orders:
                    order_id = order_data.get('order_id')
                    if order_id:
                        # Reconstruct nested 'original_signal' if it was flattened
                        original_signal = {}
                        keys_to_delete = []
                        for k, v in order_data.items():
                            if k.startswith("signal_"):
                                original_signal[k[len("signal_"):]] = v
                                keys_to_delete.append(k)
                        for k in keys_to_delete:
                            del order_data[k]
                        if original_signal:
                            order_data['original_signal'] = original_signal
                        
                        self.active_orders[order_id] = order_data
                logger.info(f"✅ Loaded {len(self.active_orders)} active orders from {self.persistence_file}")
            else:
                logger.info("ℹ️ No existing active orders persistence file found.")
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to load active orders from persistence file: {e}")
            # If loading fails, clear active_orders to prevent corrupted state
            self.active_orders = {}

    async def _place_stoploss_order(self, original_order_id: str, sl_price: float, tradingsymbol: str, quantity: int, transaction_type: str) -> Optional[str]:
        """Places a stop-loss order."""
        sl_transaction_type = "SELL" if transaction_type == "BUY" else "BUY"
        order_params = {
            "tradingsymbol": tradingsymbol,
            "transaction_type": sl_transaction_type,
            "exchange": "NFO",
            "order_type": "SL", # Stop Loss order type
            "quantity": quantity,
            "price": sl_price, # Limit price for SL order
            "triggerprice": sl_price # Trigger price for SL order
        }
        logger.info(f"Attempting to place SL order for {original_order_id}: {order_params}")
        sl_order_id = await self.place_order(order_params)
        if sl_order_id:
            self.active_orders[original_order_id]['sl_order_id'] = sl_order_id
            self.active_orders[sl_order_id] = {
                "order_id": sl_order_id,
                "status": "QUEUED",
                "filled_qty": 0,
                "avg_price": 0,
                "signal_data": self.active_orders[original_order_id].get('original_signal', {}),
                "entry_time": datetime.now().isoformat(),
                "last_update_time": datetime.now().isoformat(),
                "transaction_type": sl_transaction_type,
                "tradingsymbol": tradingsymbol,
                "quantity": quantity,
                "price": sl_price,
                "order_type": "SL",
                "product": "MIS", # Assuming MIS for SL/TP
                "exchange": "NFO",
                "variety": "NORMAL",
                "parent_order_id": original_order_id
            }
            logger.info(f"✅ SL order {sl_order_id} placed for {original_order_id}.")
            await self._save_active_orders() # Save after placing SL order
        return sl_order_id

    async def _place_target_order(self, original_order_id: str, target_price: float, tradingsymbol: str, quantity: int, transaction_type: str) -> Optional[str]:
        """Places a target profit order."""
        tp_transaction_type = "SELL" if transaction_type == "BUY" else "BUY"
        order_params = {
            "tradingsymbol": tradingsymbol,
            "transaction_type": tp_transaction_type,
            "exchange": "NFO",
            "order_type": "LIMIT", # Target is usually a limit order
            "quantity": quantity,
            "price": target_price
        }
        logger.info(f"Attempting to place TP order for {original_order_id}: {order_params}")
        tp_order_id = await self.place_order(order_params)
        if tp_order_id:
            self.active_orders[original_order_id]['tp_order_id'] = tp_order_id
            self.active_orders[tp_order_id] = {
                "order_id": tp_order_id,
                "status": "QUEUED",
                "filled_qty": 0,
                "avg_price": 0,
                "signal_data": self.active_orders[original_order_id].get('original_signal', {}),
                "entry_time": datetime.now().isoformat(),
                "last_update_time": datetime.now().isoformat(),
                "transaction_type": tp_transaction_type,
                "tradingsymbol": tradingsymbol,
                "quantity": quantity,
                "price": target_price,
                "order_type": "LIMIT",
                "product": "MIS",
                "exchange": "NFO",
                "variety": "NORMAL",
                "parent_order_id": original_order_id
            }
            logger.info(f"✅ TP order {tp_order_id} placed for {original_order_id}.")
            await self._save_active_orders() # Save after placing TP order
        return tp_order_id

    async def _monitor_sl_tp(self):
        """Monitors real-time price to trigger SL/TP and cancels unfilled legs."""
        while self.is_running:
            try:
                logger.info("🛡️ Monitoring Stop Loss and Target Profit levels...")
                # IMPORTANT: In a real system, real-time LTP (Last Traded Price) data
                # should be fetched from a dedicated market data feed (e.g., via WebSocket
                # from a Market Monitoring Agent) for accurate SL/TP monitoring and P&L calculation.
                # For this demonstration, LTP is simulated or assumed to be available.

                for order_id, order_data in list(self.active_orders.items()):
                    if order_data['status'] == 'COMPLETE' and order_data.get('original_signal'):
                        signal = order_data['original_signal']
                        tradingsymbol = order_data['tradingsymbol']
                        quantity = order_data['filled_qty']
                        transaction_type = order_data['transaction_type']
                        
                        # Simulate LTP for demonstration
                        # In reality, fetch from market data agent
                        ltp = order_data['avg_price'] # Placeholder: assume LTP is current avg price for simplicity
                        # ltp = await self.market_data_agent.get_ltp(tradingsymbol) # Example of fetching from a market data agent

                        # Place SL/TP orders if not already placed and original order is complete
                        if signal.get('stoploss') and not order_data.get('sl_order_id'):
                            await self._place_stoploss_order(order_id, signal['stoploss'], tradingsymbol, quantity, transaction_type)
                        
                        if signal.get('target') and not order_data.get('tp_order_id'):
                            await self._place_target_order(order_id, signal['target'], tradingsymbol, quantity, transaction_type)

                        # Now, monitor the placed SL/TP orders
                        sl_order_id = order_data.get('sl_order_id')
                        tp_order_id = order_data.get('tp_order_id')

                        # Check if SL/TP is hit (simplified logic, needs real LTP)
                        # This assumes the SL/TP orders themselves will update their status to COMPLETE when hit
                        if sl_order_id and self.active_orders.get(sl_order_id, {}).get('status') == 'COMPLETE':
                            logger.warning(f"🛑 Stop Loss hit for {tradingsymbol} (Order {order_id}).")
                            if tp_order_id and self.active_orders.get(tp_order_id, {}).get('status') not in ["COMPLETE", "CANCELLED", "REJECTED"]:
                                logger.info(f"Cancelling pending TP order {tp_order_id} for {order_id}.")
                                await self.cancel_order(tp_order_id)
                            self.active_orders[order_id]['status'] = "EXITED_SL"
                            self.active_orders[order_id]['exit_time'] = datetime.now().isoformat()
                            self.active_orders[order_id]['exit_price'] = self.active_orders[sl_order_id]['avg_price']
                            self._send_notification(f"🛑 SL Hit: {tradingsymbol} exited at {self.active_orders[sl_order_id]['avg_price']}. P&L: {self.active_orders[order_id].get('pnl', 'N/A')}")
                            await self._save_active_orders() # Save after exit

                        elif tp_order_id and self.active_orders.get(tp_order_id, {}).get('status') == 'COMPLETE':
                            logger.warning(f"🎯 Target Profit hit for {tradingsymbol} (Order {order_id}).")
                            if sl_order_id and self.active_orders.get(sl_order_id, {}).get('status') not in ["COMPLETE", "CANCELLED", "REJECTED"]:
                                logger.info(f"Cancelling pending SL order {sl_order_id} for {order_id}.")
                                await self.cancel_order(sl_order_id)
                            self.active_orders[order_id]['status'] = "EXITED_TP"
                            self.active_orders[order_id]['exit_time'] = datetime.now().isoformat()
                            self.active_orders[order_id]['exit_price'] = self.active_orders[tp_order_id]['avg_price']
                            self._send_notification(f"🎯 TP Hit: {tradingsymbol} exited at {self.active_orders[tp_order_id]['avg_price']}. P&L: {self.active_orders[order_id].get('pnl', 'N/A')}")
                            await self._save_active_orders() # Save after exit

                        # Dynamic SL trailing support (optional)
                        if signal.get('trailing_stoploss') and ltp:
                            await self._update_trailing_stoploss(order_id, ltp, signal['trailing_stoploss'])

                await asyncio.sleep(5) # Check every 5 seconds
            except Exception as e:
                logger.error(f"❌ [ERROR] SL/TP monitoring failed: {e}")

    async def _update_trailing_stoploss(self, order_id: str, current_ltp: float, trailing_stoploss_percentage: float):
        """
        Updates the trailing stop-loss price for an open position.
        """
        logger.info(f"📈 Updating trailing stop-loss for order {order_id} with LTP {current_ltp}...")
        
        order_data = self.active_orders.get(order_id)
        if not order_data or not order_data.get('sl_order_id'):
            logger.warning(f"⚠️ No active order or SL order found for {order_id} to trail.")
            return

        sl_order_id = order_data['sl_order_id']
        sl_order_details = self.active_orders.get(sl_order_id)

        if not sl_order_details:
            logger.warning(f"⚠️ SL order details not found for SL order ID {sl_order_id}.")
            return

        current_sl_price = sl_order_details['price']
        transaction_type = order_data['transaction_type'] # Original transaction type (BUY/SELL)

        new_sl_price = 0.0
        if transaction_type == 'BUY': # Long position, SL is below current price
            new_sl_price = current_ltp * (1 - trailing_stoploss_percentage)
            if new_sl_price > current_sl_price:
                logger.info(f"⬆️ Trailing SL for BUY order {order_id}: Old SL {current_sl_price:.2f}, New SL {new_sl_price:.2f}")
                await self.modify_order(sl_order_id, new_price=new_sl_price)
                self.active_orders[order_id]['sl_price'] = new_sl_price # Update parent order's SL price
                self._send_notification(f"📈 Trailing SL for {order_data['tradingsymbol']} updated to ₹{new_sl_price:.2f} (LTP: {current_ltp}).")
                self._log_trade_event(order_id, "TRAILING_SL_UPDATE", {"ltp": current_ltp, "trailing_pct": trailing_stoploss_percentage, "new_sl": new_sl_price})
                await self._save_active_orders() # Save after trailing SL update
            else:
                logger.info(f"ℹ️ New SL price {new_sl_price:.2f} not higher than current SL {current_sl_price:.2f} for BUY order {order_id}. No update.")
        elif transaction_type == 'SELL': # Short position, SL is above current price
            new_sl_price = current_ltp * (1 + trailing_stoploss_percentage)
            if new_sl_price < current_sl_price:
                logger.info(f"⬇️ Trailing SL for SELL order {order_id}: Old SL {current_sl_price:.2f}, New SL {new_sl_price:.2f}")
                await self.modify_order(sl_order_id, new_price=new_sl_price)
                self.active_orders[order_id]['sl_price'] = new_sl_price # Update parent order's SL price
                self._send_notification(f"📈 Trailing SL for {order_data['tradingsymbol']} updated to ₹{new_sl_price:.2f} (LTP: {current_ltp}).")
                self._log_trade_event(order_id, "TRAILING_SL_UPDATE", {"ltp": current_ltp, "trailing_pct": trailing_stoploss_percentage, "new_sl": new_sl_price})
                await self._save_active_orders() # Save after trailing SL update
            else:
                logger.info(f"ℹ️ New SL price {new_sl_price:.2f} not lower than current SL {current_sl_price:.2f} for SELL order {order_id}. No update.")

    async def _eod_square_off(self):
        """Performs end-of-day auto square-off for all open positions."""
        while self.is_running:
            now = datetime.now()
            if now >= self.eod_square_off_time and now < self.eod_square_off_time + timedelta(minutes=5): # Run within a 5-min window
                logger.info("⏰ Initiating End-of-Day Square-Off...")
                positions_to_square_off = []
                if not self.dry_run and self.smartapi_client:
                    response = self.smartapi_client.getPositions()
                    if response and response.get('status'):
                        positions_to_square_off = [p for p in response['data']['netPositions'] if int(p.get('netqty', 0)) != 0]
                elif self.dry_run:
                    # Simulate open positions for dry run
                    for order_id, order_data in self.active_orders.items():
                        if order_data['status'] == 'COMPLETE' and order_data['filled_qty'] > 0 and not order_data.get('exit_time'):
                            positions_to_square_off.append({
                                "tradingsymbol": order_data['tradingsymbol'],
                                "netqty": order_data['filled_qty'] if order_data['transaction_type'] == 'BUY' else -order_data['filled_qty'],
                                "producttype": order_data['product']
                            })

                for pos in positions_to_square_off:
                    tradingsymbol = pos['tradingsymbol']
                    net_qty = int(pos['netqty'])
                    product_type = pos['producttype']
                    
                    if net_qty != 0:
                        transaction_type = "SELL" if net_qty > 0 else "BUY"
                        quantity = abs(net_qty)
                        
                        logger.info(f"SQUARING OFF: {transaction_type} {quantity} of {tradingsymbol} ({product_type})")
                        square_off_params = {
                            "tradingsymbol": tradingsymbol,
                            "transaction_type": transaction_type,
                            "exchange": "NFO", # Assuming NFO for options
                            "order_type": "MARKET", # Market order for quick square-off
                            "quantity": quantity,
                            "product": product_type,
                            "variety": "NORMAL"
                        }
                        square_off_order_id = await self.place_order(square_off_params)
                        if square_off_order_id:
                            logger.info(f"✅ Square-off order placed for {tradingsymbol}. Order ID: {square_off_order_id}")
                            self._send_notification(f"⏰ EOD Square-Off: {tradingsymbol} ({quantity} qty) exited.")
                            # Mark the original order as exited
                            for order_id, order_data in self.active_orders.items():
                                if order_data.get('tradingsymbol') == tradingsymbol and order_data.get('status') == 'COMPLETE':
                                    self.active_orders[order_id]['status'] = "EXITED_EOD"
                                    self.active_orders[order_id]['exit_time'] = datetime.now().isoformat()
                                    # We don't have the exact exit price from the square-off order here,
                                    # but it would be the market price at the time.
                                    break
                            await self._save_active_orders() # Save after EOD square-off
                        else:
                            logger.error(f"❌ Failed to place square-off order for {tradingsymbol}.")
                
                # Sleep for a longer duration after EOD square-off to avoid re-triggering
                await asyncio.sleep(3600) # Sleep for 1 hour after EOD
            else:
                await asyncio.sleep(60) # Check every minute if it's time for EOD square-off

    async def _send_notification(self, message: str, llm_readable: bool = False, pnl_data: Optional[Dict[str, Any]] = None):
        """
        Sends notifications to various channels.
        If llm_readable is True, formats for LLM Interface Agent.
        If pnl_data is provided, formats for UI/Telegram/Dashboard.
        """
        logger.info(f"🔔 NOTIFICATION: {message}")
        
        # TODO: Integrate with actual notification clients (e.g., Telegram, Email, Dashboard)
        # Example:
        # if self.telegram_client:
        #     self.telegram_client.send_message(message)
        # if self.email_client:
        #     self.email_client.send_email("Trading Alert", message)
        # if self.dashboard_publisher:
        #     self.dashboard_publisher.publish(message)
        
        if llm_readable:
            # Format for LLM Interface Agent
            if pnl_data:
                llm_message = (
                    f"Executed {pnl_data.get('net_quantity')} lots of {pnl_data.get('tradingsymbol')} "
                    f"at ₹{pnl_data.get('entry_price')} with SL ₹{pnl_data.get('original_signal', {}).get('stoploss', 'N/A')} "
                    f"and target ₹{pnl_data.get('original_signal', {}).get('target', 'N/A')}. "
                    f"Trade confidence: {pnl_data.get('confidence', 'N/A') * 100:.0f}% "
                    f"(Strategy {pnl_data.get('strategy_id', 'N/A')}). "
                    f"Current P&L: ₹{pnl_data.get('pnl')}."
                )
            else:
                llm_message = message # Fallback to raw message
            logger.info(f"🗣️ LLM Interface Message: {llm_message}")
            # TODO: Integrate with LLM Interface Agent
            # Example:
            # if self.llm_interface_agent:
            #     self.llm_interface_agent.send_message(llm_message)

# Example usage
async def main():
    # Set dry_run=False to enable live trading (requires API keys)
    agent = OptionsExecutionAgent(dry_run=True) 
    try:
        await agent.initialize()
        # Simulate receiving a signal after initialization
        sample_signal = {
            "underlying": "BANKNIFTY",
            "strike_price": 47800,
            "option_type": "CE",
            "expiry": "2025-07-25",
            "action": "BUY",
            "entry_price": 142,
            "stoploss": 122,
            "target": 185,
            "lot_size": 2,
            "strategy_id": "strat_012",
            "confidence": 0.83,
            "timestamp": datetime.now().isoformat() # Add timestamp for latency calculation
        }
        logger.info("Simulating a signal for processing...")
        order_id = await agent._process_signal(sample_signal)
        if order_id:
            logger.info(f"Simulated signal processed, order ID: {order_id}")
        else:
            logger.warning("Simulated signal processing failed.")

        await agent.start() # This will run the monitoring loops
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user 🛑")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
