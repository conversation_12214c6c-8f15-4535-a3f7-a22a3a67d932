# Options Data Ingestion Agent Configuration
# Real-time data collection from Angel One SmartAPI

# SmartAPI Configuration
smartapi:
  # These will be loaded from environment variables
  api_key: ${SMARTAPI_API_KEY}
  client_id: ${SMARTAPI_USERNAME}
  password: ${SMARTAPI_PASSWORD}
  totp_secret: ${SMARTAPI_TOTP_TOKEN}

# Data Collection Settings
data_collection:
  # Underlying symbols to monitor
  underlying_symbols:
    - "NIFTY"
    - "BANKNIFTY"
  
  # Timeframes to generate
  timeframes:
    - "1min"
    - "3min" 
    - "5min"
    - "15min"
  
  # Real-time data settings
  realtime:
    enabled: true
    websocket_reconnect_attempts: 5
    websocket_reconnect_delay: 30  # seconds
    data_buffer_size: 1000
    tick_processing_interval: 1  # seconds
  
  # Historical data settings (fallback)
  historical:
    enabled: true
    days_to_download: 30
    chunk_size: 1000
    max_concurrent_downloads: 3

# Option Chain Settings
option_chain:
  # Strike range around ATM
  strike_range_percent: 10  # ±10% from ATM
  
  # Expiry selection
  expiries_to_monitor:
    - "current_week"
    - "next_week"
    - "current_month"
  
  # Option types
  option_types:
    - "CE"  # Call European
    - "PE"  # Put European

# Data Storage
storage:
  # Base data directory
  data_path: "data"
  
  # Live data retention
  live_data_retention_days: 7
  
  # File formats
  live_format: "json"  # For real-time streaming
  historical_format: "parquet"  # For historical data
  
  # Compression
  compression: true
  compression_level: 6

# Market Hours (IST)
market_hours:
  start_time: "09:15"
  end_time: "15:30"
  timezone: "Asia/Kolkata"
  
  # Pre-market and post-market data collection
  pre_market_enabled: false
  post_market_enabled: false

# Performance Settings
performance:
  # Memory management
  max_memory_usage_mb: 2048
  garbage_collection_interval: 300  # seconds
  
  # Processing
  batch_size: 100
  max_workers: 4
  
  # Monitoring
  performance_logging_interval: 60  # seconds

# Error Handling
error_handling:
  # Retry settings
  max_retries: 3
  retry_delay: 5  # seconds
  exponential_backoff: true
  
  # Fallback behavior
  fallback_to_historical: true
  continue_on_error: true
  
  # Alerts
  alert_on_connection_loss: true
  alert_on_data_gap: true

# Logging
logging:
  level: "INFO"
  file_rotation: true
  max_file_size_mb: 100
  backup_count: 5
  
  # Detailed logging for debugging
  log_websocket_messages: false
  log_tick_data: false
  log_performance_metrics: true

# Quality Control
quality_control:
  # Data validation
  validate_timestamps: true
  validate_price_ranges: true
  validate_volume_ranges: true
  
  # Outlier detection
  outlier_detection_enabled: true
  price_change_threshold_percent: 20  # Flag if price changes >20% in 1 minute
  volume_spike_threshold: 10  # Flag if volume >10x average
  
  # Data completeness
  min_data_completeness_percent: 95
  gap_detection_enabled: true
  max_allowed_gap_seconds: 60

# Integration
integration:
  # Market monitoring agent
  share_data_with_monitoring: true
  monitoring_update_interval: 5  # seconds
  
  # Signal generation agent
  share_data_with_signals: true
  signal_data_lag_tolerance: 10  # seconds
  
  # Risk management
  share_data_with_risk: true
  risk_data_priority: "high"

# Development & Testing
development:
  # Simulation mode (for testing without real API)
  simulation_mode: false
  simulation_data_path: "data/simulation"
  
  # Debug features
  debug_mode: false
  save_raw_websocket_data: false
  performance_profiling: false
  
  # Testing
  test_mode: false
  test_duration_minutes: 60
