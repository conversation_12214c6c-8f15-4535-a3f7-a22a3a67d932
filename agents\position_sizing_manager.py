#!/usr/bin/env python3
"""
Position Sizing and Portfolio Management Module

Features:
💰 1. Position Sizing Algorithms
- Fixed percentage risk per trade
- Kelly Criterion optimization
- Volatility-adjusted sizing
- Market cap weighted sizing
- Equal risk contribution

📊 2. Portfolio Risk Management
- Correlation-based diversification
- Sector concentration limits
- Maximum position size limits
- Portfolio heat mapping
- Risk parity allocation

⚖️ 3. Capital Allocation Strategies
- Strategic asset allocation
- Tactical allocation adjustments
- Dynamic rebalancing
- Cash management
- Margin utilization optimization

🎯 4. Risk-Adjusted Sizing
- VaR-based position sizing
- Expected shortfall limits
- Drawdown-based adjustments
- Volatility regime adaptation
- Market stress adjustments
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import json
import yaml
import numpy as np
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class SizingMethod(Enum):
    """Position sizing methods"""
    FIXED_PERCENTAGE = "fixed_percentage"
    KELLY_CRITERION = "kelly_criterion"
    VOLATILITY_ADJUSTED = "volatility_adjusted"
    EQUAL_RISK = "equal_risk"
    VAR_BASED = "var_based"
    MARKET_CAP_WEIGHTED = "market_cap_weighted"

@dataclass
class PositionSizeRequest:
    """Position sizing request"""
    symbol: str
    signal_confidence: float
    entry_price: float
    stop_loss: float
    target_price: Optional[float]
    market_volatility: float
    portfolio_value: float
    available_capital: float
    correlation_with_portfolio: float
    sector: str
    strategy_name: str

@dataclass
class PositionSizeResult:
    """Position sizing result"""
    recommended_size: int
    risk_amount: float
    risk_percentage: float
    position_value: float
    max_loss: float
    expected_return: float
    risk_reward_ratio: float
    confidence_adjusted_size: int
    volatility_adjusted_size: int
    kelly_optimal_size: int
    reasoning: List[str]

@dataclass
class PortfolioPosition:
    """Portfolio position tracking"""
    symbol: str
    quantity: int
    entry_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    sector: str
    weight: float
    risk_contribution: float
    correlation_score: float

class PositionSizingManager:
    """Advanced position sizing and portfolio management"""
    
    def __init__(self, config_path: str = "config/position_sizing_config.yaml"):
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        
        # Portfolio tracking
        self.portfolio_positions: Dict[str, PortfolioPosition] = {}
        self.sector_allocations: Dict[str, float] = {}
        self.total_portfolio_value = 0.0
        self.available_capital = 0.0
        self.used_capital = 0.0
        
        # Risk metrics
        self.portfolio_var = 0.0
        self.portfolio_volatility = 0.0
        self.max_drawdown = 0.0
        self.sharpe_ratio = 0.0
        
        # Historical data for calculations
        self.price_history: Dict[str, List[float]] = {}
        self.return_history: Dict[str, List[float]] = {}
        self.correlation_matrix: Dict[str, Dict[str, float]] = {}
        
        logger.info("💰 [INIT] Position Sizing Manager initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the position sizing manager"""
        try:
            await self._load_config()
            
            # Initialize portfolio parameters
            self.total_portfolio_value = kwargs.get('initial_balance', 100000.0)
            self.available_capital = self.total_portfolio_value
            
            logger.info("✅ [SUCCESS] Position Sizing Manager initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to initialize position sizing manager: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from YAML file"""
        default_config = {
            'position_sizing': {
                'default_method': 'fixed_percentage',
                'max_position_size_percent': 10.0,  # Max 10% per position
                'max_sector_allocation_percent': 25.0,  # Max 25% per sector
                'risk_per_trade_percent': 2.0,  # Risk 2% per trade
                'min_position_size': 1,  # Minimum lot size
                'max_position_size': 10,  # Maximum lot size
            },
            'risk_management': {
                'max_portfolio_risk_percent': 15.0,
                'correlation_threshold': 0.7,
                'volatility_lookback_days': 30,
                'rebalance_threshold_percent': 5.0,
                'cash_reserve_percent': 5.0,
            },
            'kelly_criterion': {
                'enabled': True,
                'lookback_periods': 50,
                'max_kelly_fraction': 0.25,  # Cap Kelly at 25%
                'confidence_adjustment': True,
            },
            'volatility_adjustment': {
                'enabled': True,
                'base_volatility': 0.20,  # 20% annual volatility baseline
                'volatility_multiplier': 1.5,
                'regime_adjustment': True,
            }
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    file_config = yaml.safe_load(f)
                    self.config = {**default_config, **file_config}
            except Exception as e:
                logger.warning(f"[WARNING] Failed to load config file, using defaults: {e}")
                self.config = default_config
        else:
            self.config = default_config
            logger.info("[CONFIG] Using default configuration")
    
    async def calculate_position_size(self, request: PositionSizeRequest) -> PositionSizeResult:
        """Calculate optimal position size using multiple methods"""
        try:
            logger.info(f"💰 [SIZING] Calculating position size for {request.symbol}")
            
            # Get sizing method from config
            method = SizingMethod(self.config['position_sizing']['default_method'])
            
            # Calculate using different methods
            fixed_size = await self._calculate_fixed_percentage_size(request)
            kelly_size = await self._calculate_kelly_size(request)
            volatility_size = await self._calculate_volatility_adjusted_size(request)
            var_size = await self._calculate_var_based_size(request)
            
            # Apply constraints and adjustments
            confidence_adjusted = self._apply_confidence_adjustment(fixed_size, request.signal_confidence)
            correlation_adjusted = await self._apply_correlation_adjustment(confidence_adjusted, request)
            sector_adjusted = await self._apply_sector_limits(correlation_adjusted, request)
            final_size = await self._apply_portfolio_limits(sector_adjusted, request)
            
            # Calculate risk metrics
            risk_amount = abs(request.entry_price - request.stop_loss) * final_size
            risk_percentage = (risk_amount / request.portfolio_value) * 100
            position_value = request.entry_price * final_size
            max_loss = risk_amount
            
            # Calculate expected return
            expected_return = 0.0
            risk_reward_ratio = 0.0
            if request.target_price:
                expected_return = (request.target_price - request.entry_price) * final_size
                if max_loss > 0:
                    risk_reward_ratio = expected_return / max_loss
            
            # Generate reasoning
            reasoning = [
                f"Base calculation using {method.value} method",
                f"Fixed percentage size: {fixed_size}",
                f"Kelly optimal size: {kelly_size}",
                f"Volatility adjusted size: {volatility_size}",
                f"VaR based size: {var_size}",
                f"Confidence adjustment applied: {request.signal_confidence:.2f}",
                f"Final size after all constraints: {final_size}"
            ]
            
            result = PositionSizeResult(
                recommended_size=final_size,
                risk_amount=risk_amount,
                risk_percentage=risk_percentage,
                position_value=position_value,
                max_loss=max_loss,
                expected_return=expected_return,
                risk_reward_ratio=risk_reward_ratio,
                confidence_adjusted_size=confidence_adjusted,
                volatility_adjusted_size=volatility_size,
                kelly_optimal_size=kelly_size,
                reasoning=reasoning
            )
            
            logger.info(f"✅ [SIZING] Position size calculated: {final_size} lots for {request.symbol}")
            return result
            
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to calculate position size: {e}")
            # Return conservative default
            return PositionSizeResult(
                recommended_size=1,
                risk_amount=0.0,
                risk_percentage=0.0,
                position_value=0.0,
                max_loss=0.0,
                expected_return=0.0,
                risk_reward_ratio=0.0,
                confidence_adjusted_size=1,
                volatility_adjusted_size=1,
                kelly_optimal_size=1,
                reasoning=["Error in calculation, using conservative default"]
            )
    
    async def _calculate_fixed_percentage_size(self, request: PositionSizeRequest) -> int:
        """Calculate position size using fixed percentage risk"""
        try:
            risk_per_trade = self.config['position_sizing']['risk_per_trade_percent'] / 100
            risk_amount = request.portfolio_value * risk_per_trade
            
            if request.stop_loss and request.entry_price != request.stop_loss:
                price_diff = abs(request.entry_price - request.stop_loss)
                size = int(risk_amount / price_diff)
            else:
                # Default to 1% of portfolio value
                size = int((request.portfolio_value * 0.01) / request.entry_price)
            
            # Apply min/max constraints
            min_size = self.config['position_sizing']['min_position_size']
            max_size = self.config['position_sizing']['max_position_size']
            
            return max(min_size, min(size, max_size))
            
        except Exception as e:
            logger.error(f"[ERROR] Fixed percentage calculation failed: {e}")
            return 1
    
    async def _calculate_kelly_size(self, request: PositionSizeRequest) -> int:
        """Calculate position size using Kelly Criterion"""
        try:
            if not self.config['kelly_criterion']['enabled']:
                return await self._calculate_fixed_percentage_size(request)
            
            # Simplified Kelly calculation (would need historical win rate and avg win/loss)
            # For now, use signal confidence as proxy for win probability
            win_probability = request.signal_confidence
            
            if request.target_price and request.stop_loss:
                avg_win = request.target_price - request.entry_price
                avg_loss = request.entry_price - request.stop_loss
                
                if avg_loss > 0:
                    kelly_fraction = (win_probability * avg_win - (1 - win_probability) * avg_loss) / avg_win
                    kelly_fraction = max(0, min(kelly_fraction, self.config['kelly_criterion']['max_kelly_fraction']))
                    
                    kelly_amount = request.portfolio_value * kelly_fraction
                    size = int(kelly_amount / request.entry_price)
                    
                    min_size = self.config['position_sizing']['min_position_size']
                    max_size = self.config['position_sizing']['max_position_size']
                    
                    return max(min_size, min(size, max_size))
            
            # Fallback to fixed percentage
            return await self._calculate_fixed_percentage_size(request)
            
        except Exception as e:
            logger.error(f"[ERROR] Kelly calculation failed: {e}")
            return 1

    async def _calculate_volatility_adjusted_size(self, request: PositionSizeRequest) -> int:
        """Calculate position size adjusted for volatility"""
        try:
            if not self.config['volatility_adjustment']['enabled']:
                return await self._calculate_fixed_percentage_size(request)

            base_volatility = self.config['volatility_adjustment']['base_volatility']
            volatility_multiplier = self.config['volatility_adjustment']['volatility_multiplier']

            # Adjust size based on volatility
            volatility_ratio = base_volatility / max(request.market_volatility, 0.01)
            adjusted_ratio = min(volatility_ratio * volatility_multiplier, 2.0)  # Cap at 2x

            base_size = await self._calculate_fixed_percentage_size(request)
            adjusted_size = int(base_size * adjusted_ratio)

            min_size = self.config['position_sizing']['min_position_size']
            max_size = self.config['position_sizing']['max_position_size']

            return max(min_size, min(adjusted_size, max_size))

        except Exception as e:
            logger.error(f"[ERROR] Volatility adjustment calculation failed: {e}")
            return 1

    async def _calculate_var_based_size(self, request: PositionSizeRequest) -> int:
        """Calculate position size based on Value at Risk"""
        try:
            # Simplified VaR calculation
            confidence_level = 0.95  # 95% confidence
            var_multiplier = 1.65  # Approximate 95% VaR multiplier for normal distribution

            daily_volatility = request.market_volatility / (252 ** 0.5)  # Convert annual to daily
            var_amount = request.entry_price * daily_volatility * var_multiplier

            max_var_portfolio = request.portfolio_value * (self.config['risk_management']['max_portfolio_risk_percent'] / 100)

            if var_amount > 0:
                size = int(max_var_portfolio / var_amount)
            else:
                size = 1

            min_size = self.config['position_sizing']['min_position_size']
            max_size = self.config['position_sizing']['max_position_size']

            return max(min_size, min(size, max_size))

        except Exception as e:
            logger.error(f"[ERROR] VaR calculation failed: {e}")
            return 1

    def _apply_confidence_adjustment(self, base_size: int, confidence: float) -> int:
        """Apply confidence-based adjustment to position size"""
        try:
            # Scale position size based on signal confidence
            # Confidence of 0.5 = 50% of base size, 1.0 = 100% of base size
            confidence_multiplier = max(0.1, min(confidence * 2, 1.0))
            adjusted_size = int(base_size * confidence_multiplier)

            min_size = self.config['position_sizing']['min_position_size']
            return max(min_size, adjusted_size)

        except Exception as e:
            logger.error(f"[ERROR] Confidence adjustment failed: {e}")
            return base_size

    async def _apply_correlation_adjustment(self, base_size: int, request: PositionSizeRequest) -> int:
        """Apply correlation-based adjustment to reduce concentration risk"""
        try:
            correlation_threshold = self.config['risk_management']['correlation_threshold']

            # Check correlation with existing positions
            high_correlation_exposure = 0.0
            for symbol, position in self.portfolio_positions.items():
                if position.sector == request.sector:
                    high_correlation_exposure += position.market_value

            # Reduce size if high correlation exposure exists
            correlation_ratio = high_correlation_exposure / max(self.total_portfolio_value, 1)
            if correlation_ratio > correlation_threshold:
                reduction_factor = max(0.5, 1.0 - correlation_ratio)
                adjusted_size = int(base_size * reduction_factor)
            else:
                adjusted_size = base_size

            min_size = self.config['position_sizing']['min_position_size']
            return max(min_size, adjusted_size)

        except Exception as e:
            logger.error(f"[ERROR] Correlation adjustment failed: {e}")
            return base_size

    async def _apply_sector_limits(self, base_size: int, request: PositionSizeRequest) -> int:
        """Apply sector allocation limits"""
        try:
            max_sector_allocation = self.config['position_sizing']['max_sector_allocation_percent'] / 100

            # Calculate current sector exposure
            current_sector_exposure = self.sector_allocations.get(request.sector, 0.0)
            position_value = request.entry_price * base_size

            # Check if adding this position would exceed sector limit
            new_sector_exposure = (current_sector_exposure + position_value) / self.total_portfolio_value

            if new_sector_exposure > max_sector_allocation:
                # Reduce size to stay within sector limit
                max_additional_value = (max_sector_allocation * self.total_portfolio_value) - current_sector_exposure
                if max_additional_value > 0:
                    adjusted_size = int(max_additional_value / request.entry_price)
                else:
                    adjusted_size = 0
            else:
                adjusted_size = base_size

            min_size = self.config['position_sizing']['min_position_size']
            return max(0, max(min_size if adjusted_size > 0 else 0, adjusted_size))

        except Exception as e:
            logger.error(f"[ERROR] Sector limit adjustment failed: {e}")
            return base_size

    async def _apply_portfolio_limits(self, base_size: int, request: PositionSizeRequest) -> int:
        """Apply overall portfolio limits"""
        try:
            max_position_percent = self.config['position_sizing']['max_position_size_percent'] / 100
            max_position_value = self.total_portfolio_value * max_position_percent

            position_value = request.entry_price * base_size

            if position_value > max_position_value:
                adjusted_size = int(max_position_value / request.entry_price)
            else:
                adjusted_size = base_size

            # Check available capital
            if position_value > request.available_capital:
                adjusted_size = int(request.available_capital / request.entry_price)

            min_size = self.config['position_sizing']['min_position_size']
            max_size = self.config['position_sizing']['max_position_size']

            return max(0, max(min_size if adjusted_size > 0 else 0, min(adjusted_size, max_size)))

        except Exception as e:
            logger.error(f"[ERROR] Portfolio limit adjustment failed: {e}")
            return base_size

    async def update_portfolio_position(self, symbol: str, quantity: int, entry_price: float,
                                      current_price: float, sector: str):
        """Update portfolio position"""
        try:
            market_value = quantity * current_price
            unrealized_pnl = (current_price - entry_price) * quantity
            weight = market_value / max(self.total_portfolio_value, 1)

            position = PortfolioPosition(
                symbol=symbol,
                quantity=quantity,
                entry_price=entry_price,
                current_price=current_price,
                market_value=market_value,
                unrealized_pnl=unrealized_pnl,
                sector=sector,
                weight=weight,
                risk_contribution=0.0,  # TODO: Calculate risk contribution
                correlation_score=0.0   # TODO: Calculate correlation score
            )

            self.portfolio_positions[symbol] = position
            await self._update_sector_allocations()
            await self._update_portfolio_metrics()

        except Exception as e:
            logger.error(f"[ERROR] Failed to update portfolio position: {e}")

    async def remove_portfolio_position(self, symbol: str):
        """Remove portfolio position"""
        try:
            if symbol in self.portfolio_positions:
                del self.portfolio_positions[symbol]
                await self._update_sector_allocations()
                await self._update_portfolio_metrics()
                logger.info(f"[PORTFOLIO] Removed position: {symbol}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to remove portfolio position: {e}")

    async def _update_sector_allocations(self):
        """Update sector allocation tracking"""
        try:
            self.sector_allocations.clear()

            for position in self.portfolio_positions.values():
                if position.sector not in self.sector_allocations:
                    self.sector_allocations[position.sector] = 0.0
                self.sector_allocations[position.sector] += position.market_value

        except Exception as e:
            logger.error(f"[ERROR] Failed to update sector allocations: {e}")

    async def _update_portfolio_metrics(self):
        """Update portfolio-level risk metrics"""
        try:
            # Calculate total portfolio value
            total_market_value = sum(pos.market_value for pos in self.portfolio_positions.values())
            self.used_capital = total_market_value

            # TODO: Calculate portfolio volatility, VaR, etc.

        except Exception as e:
            logger.error(f"[ERROR] Failed to update portfolio metrics: {e}")

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        try:
            total_positions = len(self.portfolio_positions)
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.portfolio_positions.values())

            # Top sectors by allocation
            sorted_sectors = sorted(self.sector_allocations.items(), key=lambda x: x[1], reverse=True)

            # Find largest position
            largest_position = None
            if self.portfolio_positions:
                largest_position = max(self.portfolio_positions.values(), key=lambda x: x.market_value)

            return {
                'total_portfolio_value': self.total_portfolio_value,
                'available_capital': self.available_capital,
                'used_capital': self.used_capital,
                'total_positions': total_positions,
                'total_unrealized_pnl': total_unrealized_pnl,
                'sector_allocations': dict(sorted_sectors),
                'portfolio_utilization': (self.used_capital / max(self.total_portfolio_value, 1)) * 100,
                'largest_position': largest_position.symbol if largest_position else None,
                'largest_position_value': largest_position.market_value if largest_position else 0,
                'portfolio_var': self.portfolio_var,
                'portfolio_volatility': self.portfolio_volatility
            }

        except Exception as e:
            logger.error(f"[ERROR] Failed to get portfolio summary: {e}")
            return {}

    def check_position_limits(self, symbol: str, proposed_size: int, entry_price: float, sector: str) -> Dict[str, Any]:
        """Check if proposed position violates any limits"""
        try:
            violations = []
            warnings = []

            proposed_value = proposed_size * entry_price

            # Check maximum position size
            max_position_percent = self.config['position_sizing']['max_position_size_percent'] / 100
            max_position_value = self.total_portfolio_value * max_position_percent

            if proposed_value > max_position_value:
                violations.append(f"Position size exceeds maximum limit: {proposed_value:,.2f} > {max_position_value:,.2f}")

            # Check sector allocation
            max_sector_allocation = self.config['position_sizing']['max_sector_allocation_percent'] / 100
            current_sector_exposure = self.sector_allocations.get(sector, 0.0)
            new_sector_exposure = (current_sector_exposure + proposed_value) / self.total_portfolio_value

            if new_sector_exposure > max_sector_allocation:
                violations.append(f"Sector allocation exceeds limit: {new_sector_exposure*100:.1f}% > {max_sector_allocation*100:.1f}%")

            # Check available capital
            if proposed_value > self.available_capital:
                violations.append(f"Insufficient capital: {proposed_value:,.2f} > {self.available_capital:,.2f}")

            # Check portfolio utilization
            new_utilization = (self.used_capital + proposed_value) / self.total_portfolio_value
            if new_utilization > 0.95:  # 95% utilization warning
                warnings.append(f"High portfolio utilization: {new_utilization*100:.1f}%")

            return {
                'approved': len(violations) == 0,
                'violations': violations,
                'warnings': warnings,
                'proposed_value': proposed_value,
                'max_allowed_value': max_position_value,
                'sector_exposure_after': new_sector_exposure * 100,
                'portfolio_utilization_after': new_utilization * 100
            }

        except Exception as e:
            logger.error(f"[ERROR] Failed to check position limits: {e}")
            return {'approved': False, 'violations': ['Error in limit checking'], 'warnings': []}


# Example usage
async def main():
    """Example usage of Position Sizing Manager"""
    manager = PositionSizingManager()

    try:
        await manager.initialize(initial_balance=100000.0)

        # Example position sizing request
        request = PositionSizeRequest(
            symbol="NIFTY24800CE",
            signal_confidence=0.75,
            entry_price=150.0,
            stop_loss=140.0,
            target_price=170.0,
            market_volatility=0.25,
            portfolio_value=100000.0,
            available_capital=80000.0,
            correlation_with_portfolio=0.3,
            sector="INDEX_OPTIONS",
            strategy_name="momentum_breakout"
        )

        result = await manager.calculate_position_size(request)
        print(f"Recommended position size: {result.recommended_size}")
        print(f"Risk amount: ₹{result.risk_amount:,.2f}")
        print(f"Risk percentage: {result.risk_percentage:.2f}%")

        # Check position limits
        limits_check = manager.check_position_limits(
            request.symbol, result.recommended_size, request.entry_price, request.sector
        )
        print(f"Position approved: {limits_check['approved']}")

    except Exception as e:
        logger.error(f"Error in example: {e}")


if __name__ == "__main__":
    asyncio.run(main())
