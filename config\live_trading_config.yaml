# 🚦 Live Trading System Configuration
# Comprehensive configuration for the automated options trading system

# Trading Mode Settings
mode: "paper" # "paper" for virtual trading, "real" for live trading with Angel One SmartAPI
capital: 100000.0 # Starting capital for paper trading mode (₹)

# Trading Hours (IST - Indian Standard Time)
run_duration: "09:15 to 15:15" # Market open to close
eod_square_off_time: "15:20" # Time to square off all open positions at end of day
signal_check_interval_seconds: 5 # How often to check for new signals and process them

# Market Settings
market_settings:
  nifty:
    lot_size: 50
    tick_size: 0.05
    margin_required: 15000
  banknifty:
    lot_size: 25
    tick_size: 0.05
    margin_required: 25000

# Paper Trading Settings
paper_trading:
  brokerage_rate: 0.0003  # 0.03% brokerage
  slippage_rate: 0.001    # 0.1% slippage simulation
  enable_realistic_fills: true
  simulate_market_impact: true

# LLM Integration
enable_llm_summary: true # Enable/disable daily LLM summaries and interactive features
llm_query_interval: 900  # 15 minutes between LLM market insights
llm_models:
  general: "llama3.1:8b"
  analysis: "llama3.1:8b"
  strategy: "llama3.1:8b"

# Safety and Fail-Safe Parameters
safety_checks:
  api_disconnection_threshold_minutes: 5 # If no market data updates for this duration, abort trading
  max_drawdown_percent: 0.10 # Abort trading if total capital drawdown exceeds this percentage (e.g., 0.10 for 10%)
  consecutive_sl_hits_limit: 3 # Abort trading if this many consecutive stop losses are hit
  vix_spike_threshold: 0.25 # Abort trading if implied volatility (VIX) exceeds this threshold
  max_positions_per_underlying: 3
  max_capital_per_trade_percent: 0.05  # 5% max capital per trade

# Agent Configuration
agents:
  market_monitoring:
    enabled: true
    timeframes: ["1min", "3min", "5min", "15min"]
    underlying_symbols: ["NIFTY", "BANKNIFTY"]
    data_update_interval: 60

  signal_generation:
    enabled: true
    confidence_threshold: 0.55  # Reduced from 0.7 to 0.55 for better signal generation
    signal_types: ["volatility", "directional", "flow", "greeks"]
    model_inference_enabled: true

  risk_management:
    enabled: true
    real_time_monitoring: true
    position_size_limits: true

  execution:
    enabled: true
    order_timeout_seconds: 30
    retry_attempts: 3

  performance_analysis:
    enabled: true
    analysis_interval: 300
    metrics: ["sharpe", "sortino", "calmar", "max_drawdown"]

  ai_training:
    enabled: false  # Disable during live trading

  llm_interface:
    enabled: true
    interactive_mode: true

# Logging and Reporting
log_level: "INFO" # DEBUG, INFO, WARNING, ERROR, CRITICAL
trade_log_path: "logs/trades.log"
daily_summary_output_dir: "exports/reports"
logging:
  file_rotation: true
  max_file_size_mb: 100
  backup_count: 5
  log_directories:
    main: "logs"
    trades: "logs/trades"
    signals: "logs/signals"
    errors: "logs/errors"

# Data Storage Settings
data_storage:
  base_path: "data"
  backup_enabled: true
  compression: true
  retention_days: 90

# Notification Settings
notifications:
  console: true
  file: true
  llm_alerts: true
  critical_only_during_off_hours: true

# Performance Monitoring
performance:
  real_time_metrics: true
  benchmark: "NIFTY"
  risk_free_rate: 0.05
  update_interval_seconds: 60

# Strategy Settings
strategies:
  enabled_strategies: ["momentum", "mean_reversion", "volatility_breakout"]
  max_concurrent_strategies: 3
  strategy_allocation_percent: 0.33  # Equal allocation

# API Settings (for real trading)
api:
  smartapi:
    base_url: "https://apiconnect.angelbroking.com"
    timeout_seconds: 30
    rate_limit_per_second: 10

# Windows-specific settings
windows:
  use_emoji: true
  console_encoding: "utf-8"
  file_encoding: "utf-8"
