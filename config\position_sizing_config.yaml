# Position Sizing and Portfolio Management Configuration
# Advanced position sizing algorithms and portfolio risk management

# Position Sizing Settings
position_sizing:
  # Default sizing method
  default_method: "fixed_percentage"  # Options: fixed_percentage, kelly_criterion, volatility_adjusted, equal_risk, var_based
  
  # Position size limits
  max_position_size_percent: 10.0     # Maximum 10% of portfolio per position
  max_sector_allocation_percent: 25.0  # Maximum 25% of portfolio per sector
  risk_per_trade_percent: 2.0         # Risk 2% of portfolio per trade
  min_position_size: 1                # Minimum lot size
  max_position_size: 10               # Maximum lot size
  
  # Capital allocation
  max_capital_utilization: 90.0       # Maximum 90% capital utilization
  cash_reserve_percent: 10.0          # Keep 10% cash reserve
  emergency_reserve_percent: 5.0      # Emergency cash reserve

# Risk Management Settings
risk_management:
  # Portfolio risk limits
  max_portfolio_risk_percent: 15.0    # Maximum portfolio risk exposure
  max_daily_var_percent: 3.0          # Maximum daily VaR
  max_drawdown_percent: 10.0          # Maximum portfolio drawdown
  
  # Correlation and concentration limits
  correlation_threshold: 0.7          # Reduce size if correlation > 70%
  max_single_stock_weight: 15.0       # Maximum single stock weight
  max_sector_concentration: 30.0      # Maximum sector concentration
  
  # Volatility and timing
  volatility_lookback_days: 30        # Days for volatility calculation
  rebalance_threshold_percent: 5.0    # Rebalance when allocation drifts > 5%
  position_decay_days: 30             # Reduce size for old positions
  
  # Risk monitoring
  monitor_real_time_risk: true
  calculate_portfolio_beta: true
  track_sector_rotation: true
  monitor_correlation_breakdown: true

# Kelly Criterion Settings
kelly_criterion:
  enabled: true
  lookback_periods: 50                # Historical periods for calculation
  max_kelly_fraction: 0.25           # Cap Kelly at 25% of portfolio
  min_kelly_fraction: 0.01           # Minimum Kelly fraction
  confidence_adjustment: true         # Adjust for signal confidence
  win_rate_smoothing: 0.1            # Exponential smoothing factor
  
  # Kelly modifications
  fractional_kelly: 0.5              # Use 50% of full Kelly
  kelly_floor: 0.02                  # Minimum position size even if Kelly suggests 0
  kelly_ceiling: 0.15                # Maximum position size even if Kelly suggests more

# Volatility Adjustment Settings
volatility_adjustment:
  enabled: true
  base_volatility: 0.20              # 20% annual volatility baseline
  volatility_multiplier: 1.5         # Volatility adjustment multiplier
  regime_adjustment: true            # Adjust for volatility regimes
  
  # Volatility regimes
  low_vol_threshold: 0.15            # Below 15% = low volatility
  high_vol_threshold: 0.35           # Above 35% = high volatility
  low_vol_multiplier: 1.2            # Increase size in low vol
  high_vol_multiplier: 0.8           # Decrease size in high vol
  
  # Volatility calculation
  vol_calculation_method: "ewma"      # Options: simple, ewma, garch
  ewma_lambda: 0.94                  # EWMA decay factor
  vol_floor: 0.05                    # Minimum volatility assumption
  vol_ceiling: 1.0                   # Maximum volatility assumption

# Value at Risk (VaR) Settings
var_settings:
  enabled: true
  confidence_level: 0.95             # 95% confidence level
  time_horizon_days: 1               # 1-day VaR
  calculation_method: "parametric"    # Options: parametric, historical, monte_carlo
  
  # Historical VaR
  historical_lookback_days: 252      # 1 year of data
  
  # Monte Carlo VaR
  monte_carlo_simulations: 10000     # Number of simulations
  
  # VaR limits
  max_individual_var_percent: 2.0    # Max VaR per position
  max_portfolio_var_percent: 5.0     # Max portfolio VaR

# Dynamic Position Sizing
dynamic_sizing:
  enabled: true
  
  # Market regime adjustments
  bull_market_multiplier: 1.2        # Increase size in bull markets
  bear_market_multiplier: 0.8        # Decrease size in bear markets
  sideways_market_multiplier: 1.0    # Normal size in sideways markets
  
  # Performance-based adjustments
  winning_streak_multiplier: 1.1     # Increase size after wins
  losing_streak_multiplier: 0.9      # Decrease size after losses
  max_performance_multiplier: 1.5    # Maximum performance adjustment
  min_performance_multiplier: 0.5    # Minimum performance adjustment
  
  # Time-based adjustments
  intraday_size_reduction: 0.9       # Reduce size for intraday trades
  overnight_size_reduction: 0.8      # Reduce size for overnight positions
  weekend_size_reduction: 0.7        # Reduce size over weekends

# Sector and Asset Class Limits
sector_limits:
  # Indian market sectors
  BANKING: 20.0                      # Maximum 20% in banking
  IT: 15.0                          # Maximum 15% in IT
  PHARMA: 10.0                      # Maximum 10% in pharma
  AUTO: 10.0                        # Maximum 10% in auto
  METALS: 8.0                       # Maximum 8% in metals
  ENERGY: 8.0                       # Maximum 8% in energy
  FMCG: 12.0                        # Maximum 12% in FMCG
  REALTY: 5.0                       # Maximum 5% in realty
  TELECOM: 5.0                      # Maximum 5% in telecom
  INDEX_OPTIONS: 30.0               # Maximum 30% in index options
  STOCK_OPTIONS: 20.0               # Maximum 20% in stock options
  
  # Asset class limits
  EQUITY: 70.0                      # Maximum 70% in equity
  OPTIONS: 50.0                     # Maximum 50% in options
  FUTURES: 30.0                     # Maximum 30% in futures
  CASH: 30.0                        # Maximum 30% in cash

# Portfolio Optimization
portfolio_optimization:
  enabled: true
  optimization_method: "mean_variance"  # Options: mean_variance, risk_parity, black_litterman
  
  # Mean variance optimization
  expected_return_method: "historical"  # Options: historical, capm, factor_model
  covariance_method: "sample"          # Options: sample, shrinkage, factor_model
  risk_aversion: 3.0                   # Risk aversion parameter
  
  # Risk parity
  risk_parity_method: "equal_risk"     # Options: equal_risk, hierarchical
  
  # Rebalancing
  rebalance_frequency: "weekly"        # Options: daily, weekly, monthly
  rebalance_threshold: 0.05           # Rebalance when drift > 5%
  transaction_cost_bps: 10            # Transaction costs in basis points

# Advanced Features
advanced_features:
  # Machine learning integration
  ml_position_sizing: false          # Use ML for position sizing
  ml_model_path: "models/position_sizing_model.pkl"
  
  # Options-specific sizing
  options_delta_adjustment: true     # Adjust for options delta
  options_gamma_adjustment: true     # Adjust for options gamma
  options_theta_adjustment: true     # Adjust for options theta
  options_vega_adjustment: true      # Adjust for options vega
  
  # Greeks-based sizing
  max_portfolio_delta: 100           # Maximum portfolio delta
  max_portfolio_gamma: 50            # Maximum portfolio gamma
  max_portfolio_vega: 1000           # Maximum portfolio vega
  
  # Liquidity adjustments
  liquidity_adjustment: true         # Adjust for liquidity
  min_avg_volume: 100000            # Minimum average daily volume
  liquidity_discount: 0.1           # Reduce size for illiquid assets

# Backtesting and Validation
backtesting:
  enabled: true
  backtest_period_days: 252         # 1 year backtest
  walk_forward_analysis: true       # Use walk-forward optimization
  out_of_sample_percent: 20         # 20% out-of-sample testing
  
  # Performance metrics
  calculate_sharpe_ratio: true
  calculate_sortino_ratio: true
  calculate_calmar_ratio: true
  calculate_max_drawdown: true
  calculate_var: true
  calculate_cvar: true

# Logging and Monitoring
logging:
  log_position_sizing_decisions: true
  log_portfolio_changes: true
  log_risk_metrics: true
  log_performance_attribution: true
  
  # Log files
  position_sizing_log: "logs/position_sizing.log"
  portfolio_log: "logs/portfolio.log"
  risk_log: "logs/risk_metrics.log"

# Integration Settings
integration:
  # Risk management integration
  sync_with_risk_manager: true
  
  # Execution integration
  sync_with_execution_agent: true
  
  # Performance tracking integration
  sync_with_performance_tracker: true
  
  # Real-time updates
  real_time_position_updates: true
  real_time_risk_updates: true
