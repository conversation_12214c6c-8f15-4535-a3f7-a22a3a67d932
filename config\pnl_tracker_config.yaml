# Real-time P&L Tracker Configuration
# Comprehensive P&L tracking and performance monitoring

# Update Intervals
update_intervals:
  pnl_update_seconds: 5              # P&L update frequency
  performance_calculation_seconds: 60  # Performance metrics calculation
  risk_monitoring_seconds: 30        # Risk monitoring frequency
  market_data_refresh_seconds: 1     # Market data refresh rate
  
# Performance Calculation Settings
performance_metrics:
  # Risk-free rate for Sharpe/Sortino calculations
  risk_free_rate: 0.05               # Annual risk-free rate (5%)
  
  # VaR and CVaR settings
  var_confidence_level: 0.95         # 95% confidence level
  cvar_confidence_level: 0.95        # 95% confidence level
  
  # Lookback periods for calculations
  lookback_periods:
    sharpe_ratio_days: 252           # 1 year for Sharpe ratio
    volatility_days: 30              # 30 days for volatility
    drawdown_days: 252               # 1 year for drawdown
    correlation_days: 60             # 60 days for correlation
    beta_days: 252                   # 1 year for beta calculation
  
  # Performance benchmarks
  benchmark_symbol: "NIFTY50"        # Benchmark for comparison
  benchmark_return: 0.12             # Expected annual benchmark return
  
  # Rolling window settings
  rolling_windows:
    short_term_days: 7               # Short-term performance window
    medium_term_days: 30             # Medium-term performance window
    long_term_days: 90               # Long-term performance window

# Alert Thresholds
alerts:
  # Drawdown alerts
  max_drawdown_percent: 10.0         # Alert when drawdown exceeds 10%
  daily_drawdown_percent: 5.0        # Alert when daily drawdown exceeds 5%
  intraday_drawdown_percent: 3.0     # Alert when intraday drawdown exceeds 3%
  
  # Loss alerts
  daily_loss_percent: 5.0            # Alert when daily loss exceeds 5%
  position_loss_percent: 20.0        # Alert when single position loss exceeds 20%
  portfolio_loss_percent: 8.0        # Alert when portfolio loss exceeds 8%
  
  # Risk metric alerts
  var_breach_threshold: 1.5          # Alert when VaR is breached by 1.5x
  sharpe_ratio_threshold: 0.5        # Alert when Sharpe ratio drops below 0.5
  volatility_threshold: 0.30         # Alert when volatility exceeds 30%
  
  # Position alerts
  max_position_concentration: 25.0   # Alert when single position > 25% of portfolio
  max_sector_concentration: 40.0     # Alert when sector concentration > 40%
  correlation_threshold: 0.8         # Alert when position correlation > 0.8
  
  # Performance alerts
  consecutive_losses_threshold: 5     # Alert after 5 consecutive losses
  win_rate_threshold: 40.0          # Alert when win rate drops below 40%
  profit_factor_threshold: 1.2      # Alert when profit factor drops below 1.2

# Transaction Costs
transaction_costs:
  # Commission settings
  commission_per_trade: 20.0         # Fixed commission per trade (INR)
  commission_percentage: 0.0         # Percentage-based commission
  
  # Slippage settings
  slippage_bps: 5.0                 # Expected slippage in basis points
  market_impact_factor: 0.1         # Market impact factor for large orders
  
  # Other costs
  stt_rate: 0.1                     # Securities Transaction Tax rate (%)
  exchange_charges: 0.00345         # Exchange charges (%)
  gst_rate: 18.0                    # GST on brokerage (%)
  
  # Cost calculation method
  include_all_costs: true           # Include all transaction costs in P&L
  real_time_cost_calculation: true  # Calculate costs in real-time

# Greeks Calculation
greeks_settings:
  # Greeks P&L attribution
  calculate_greeks_pnl: true        # Enable Greeks P&L calculation
  greeks_update_frequency: 30       # Update Greeks every 30 seconds
  
  # Greeks sensitivity analysis
  delta_sensitivity: true           # Track delta P&L
  gamma_sensitivity: true           # Track gamma P&L
  theta_sensitivity: true           # Track theta P&L
  vega_sensitivity: true            # Track vega P&L
  rho_sensitivity: false            # Track rho P&L (usually less important)
  
  # Greeks calculation parameters
  risk_free_rate: 0.05              # Risk-free rate for Greeks calculation
  dividend_yield: 0.0               # Dividend yield assumption
  
  # Greeks smoothing
  smooth_greeks: true               # Apply smoothing to Greeks values
  smoothing_factor: 0.1             # Exponential smoothing factor

# Risk Management Integration
risk_management:
  # Position sizing integration
  integrate_position_sizing: true    # Integrate with position sizing manager
  
  # Risk limits
  max_portfolio_var: 0.05           # Maximum portfolio VaR (5%)
  max_individual_var: 0.02          # Maximum individual position VaR (2%)
  
  # Stress testing
  stress_test_scenarios:
    market_crash: -0.20             # 20% market drop scenario
    volatility_spike: 2.0           # 2x volatility increase
    interest_rate_shock: 0.02       # 200 bps rate change
    liquidity_crisis: 0.5           # 50% liquidity reduction
  
  # Risk monitoring
  real_time_risk_monitoring: true   # Enable real-time risk monitoring
  risk_limit_enforcement: true      # Enforce risk limits automatically

# Data Storage and Persistence
data_storage:
  # Database settings
  use_database: false               # Use database for storage
  database_url: ""                  # Database connection URL
  
  # File storage settings
  save_to_files: true               # Save data to files
  data_directory: "data/pnl_tracker"  # Data storage directory
  
  # Data retention
  retention_days: 365               # Keep data for 1 year
  auto_cleanup: true                # Automatically clean up old data
  
  # Backup settings
  backup_enabled: true              # Enable data backup
  backup_frequency_hours: 6         # Backup every 6 hours
  backup_location: "backups/pnl_tracker"  # Backup directory
  
  # Export settings
  export_formats: ["json", "csv"]   # Export formats
  auto_export_daily: true           # Auto-export daily summaries
  export_location: "exports/pnl_tracker"  # Export directory

# Performance Reporting
reporting:
  # Report generation
  generate_daily_reports: true      # Generate daily P&L reports
  generate_weekly_reports: true     # Generate weekly performance reports
  generate_monthly_reports: true    # Generate monthly analysis reports
  
  # Report content
  include_charts: true              # Include charts in reports
  include_detailed_trades: true     # Include detailed trade information
  include_risk_metrics: true        # Include risk analysis
  include_attribution: true         # Include performance attribution
  
  # Report delivery
  email_reports: false              # Email reports automatically
  save_reports_locally: true        # Save reports to local directory
  report_directory: "reports/pnl_tracker"  # Report storage directory
  
  # Report formats
  report_formats: ["html", "pdf"]   # Report output formats

# Integration Settings
integration:
  # System status monitor integration
  system_monitor_integration: true  # Integrate with system status monitor
  
  # Alerting system integration
  alerting_system_integration: true # Integrate with alerting system
  
  # Performance analysis agent integration
  performance_agent_integration: true  # Integrate with performance analysis agent
  
  # Broker integration
  broker_integration: true          # Integrate with broker for real-time data
  
  # External data sources
  market_data_sources: ["broker", "external_api"]  # Market data sources
  
  # API settings
  api_enabled: false                # Enable REST API for external access
  api_port: 8080                    # API port
  api_authentication: false         # Require API authentication

# Advanced Features
advanced_features:
  # Machine learning integration
  ml_performance_prediction: false  # Use ML for performance prediction
  ml_risk_prediction: false         # Use ML for risk prediction
  
  # Real-time analytics
  real_time_correlation_analysis: true   # Real-time correlation analysis
  real_time_attribution_analysis: true  # Real-time attribution analysis
  
  # Portfolio optimization
  dynamic_rebalancing: false        # Dynamic portfolio rebalancing
  risk_parity_monitoring: false     # Risk parity monitoring
  
  # Custom metrics
  custom_metrics_enabled: false     # Enable custom performance metrics
  custom_metrics_file: "config/custom_metrics.py"  # Custom metrics definition

# Visualization Settings
visualization:
  # Chart generation
  generate_charts: true             # Generate performance charts
  chart_update_frequency: 300       # Update charts every 5 minutes
  
  # Chart types
  pnl_curve_chart: true            # P&L curve chart
  drawdown_chart: true             # Drawdown chart
  position_heatmap: true           # Position performance heatmap
  correlation_matrix: true         # Correlation matrix chart
  
  # Chart settings
  chart_theme: "dark"              # Chart theme: light, dark
  chart_resolution: "high"         # Chart resolution: low, medium, high
  chart_format: "png"              # Chart format: png, svg, pdf
  
  # Real-time dashboard
  real_time_dashboard: true        # Enable real-time dashboard
  dashboard_refresh_seconds: 10    # Dashboard refresh frequency

# Testing and Development
testing:
  # Test mode settings
  test_mode: false                 # Enable test mode
  mock_market_data: false          # Use mock market data
  
  # Simulation settings
  simulate_slippage: true          # Simulate realistic slippage
  simulate_commission: true        # Simulate commission costs
  
  # Development features
  debug_mode: false                # Enable debug mode
  verbose_logging: false           # Enable verbose logging
  log_all_calculations: false      # Log all P&L calculations

# Security and Privacy
security:
  # Data encryption
  encrypt_sensitive_data: false    # Encrypt sensitive P&L data
  
  # Access control
  require_authentication: false    # Require authentication for access
  
  # Audit trail
  audit_trail_enabled: true        # Enable audit trail
  audit_log_file: "logs/pnl_audit.log"  # Audit log file
  
  # Data privacy
  anonymize_data: false            # Anonymize exported data
  gdpr_compliance: false           # Enable GDPR compliance features
