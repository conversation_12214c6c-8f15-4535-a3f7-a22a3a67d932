# Options Market Monitoring Agent Configuration

# Underlying symbols to monitor
underlying_symbols:
  - NIFTY
  - BANKNIFTY

# Monitoring intervals for different timeframes (in seconds)
monitoring_intervals:
  1min: 60
  3min: 180
  5min: 300
  15min: 900

# Alert thresholds for various market conditions
alert_thresholds:
  volatility_spike: 0.20
  volume_spike: 2.0
  price_change: 0.05
  greeks_threshold:
    delta: 0.10
    gamma: 0.05
    theta: 0.02
    vega: 0.15
  oi_spike_threshold: 0.30 # 30% increase in OI
  iv_explosion_threshold: 0.25 # 25% increase in IV

# Market regime detection parameters
market_regime_params:
  trend_lookback: 20 # Periods for trend detection (e.g., 20-period SMA)
  volatility_lookback: 14 # Periods for IV analysis
  liquidity_volume_threshold: 0.1 # Percentage of average volume for low liquidity
  momentum_rsi_threshold: 70 # RSI overbought threshold for exhaustion
  momentum_macd_signal_cross_period: 9 # MACD signal line cross period

# Time-aware monitoring parameters
time_aware_params:
  opening_range_minutes: 15 # First 15 minutes after market open
  power_hour_start_minutes_before_close: 60 # Last 60 minutes before market close
  expiry_warning_days: 3 # Warn if expiry is within 3 days

# Anomaly detection parameters
anomaly_detection_params:
  spread_deviation_multiplier: 2.0 # Spread deviation from average
  oi_price_divergence_threshold: 0.01 # Price move threshold for OI spike
  iv_price_divergence_threshold: 0.005 # Price move threshold for IV spike
  algo_spike_depth_change_threshold: 0.5 # 50% rapid depth change

# Output and logging settings
output_settings:
  summary_output_path: "data/live/market_summary.json"
  log_retraining_data: true
  retraining_data_path: "data/ai_training/market_monitoring_logs.parquet"
