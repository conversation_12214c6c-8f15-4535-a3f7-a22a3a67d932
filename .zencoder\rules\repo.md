---
description: Repository Information Overview
alwaysApply: true
---

# NIFTY & BANK NIFTY Options Trading System

## Summary
Advanced AI-powered options trading system for Nifty 50 and Bank Nifty options with comprehensive backtesting, real-time signal generation, risk management, and automated execution capabilities. The system leverages machine learning, high-performance data processing, and advanced options strategies.

## Structure
- **agents/**: Options trading agents for various functions (monitoring, signals, risk, execution)
- **config/**: Configuration files for different system components
- **data/**: Storage for historical/live data, option chains, Greeks, volatility data
- **docs/**: Documentation including guides and API references
- **logs/**: System logs organized by date and component
- **tests/**: Test suite with unit and integration tests
- **utils/**: Utility modules for options pricing, data management, etc.
- **serena/**: Embedded subproject with its own structure and dependencies

## Language & Runtime
**Language**: Python 3.12+
**Build System**: Standard Python setuptools
**Package Manager**: pip
**Data Processing**: Polars + PyArrow for high-performance operations

## Dependencies
**Main Dependencies**:
- **Data Processing**: polars (≥0.20.0), pyarrow (≥15.0.0)
- **ML/AI**: lightgbm (≥4.3.0), torch (≥2.2.0), scikit-learn (≥1.4.0)
- **Options-Specific**: py_vollib (≥1.0.1), QuantLib-Python (≥1.32)
- **API Integration**: smartapi-python (≥1.4.8), websocket-client (≥1.7.0)
- **Web Framework**: fastapi (≥0.109.0), uvicorn (≥0.27.0)

**Development Dependencies**:
- pytest (≥8.0.0), black (≥24.0.0), flake8 (≥7.0.0), mypy (≥1.8.0)

## Build & Installation
```bash
# Basic installation
pip install -r requirements.txt

# For GPU acceleration (NVIDIA GPU with CUDA 11.8+)
# Uncomment GPU sections in requirements.txt
pip install -r requirements.txt

# Verify installation
python -c "import lightgbm, polars, torch, py_vollib; print('✅ Installation successful')"
```

## Testing
**Framework**: pytest with custom fixtures
**Test Location**: tests/ directory
**Naming Convention**: test_*.py files
**Configuration**: tests/conftest.py
**Run Command**:
```bash
pytest tests/
```

## Main Entry Points
**Main System**: main.py (OptionsSystemOrchestrator)
**Data Download**: download_historical_data.py, download_options_with_expiry_input.py
**Live Trading**: run_live_trading.py
**Sample Data**: generate_sample_data.py

## Key Features
- **Real-time Options Trading**: Live option chain monitoring and signal generation
- **AI-Powered Strategies**: ML-based options strategy selection and optimization
- **High Performance**: Built with Polars, PyArrow, and GPU acceleration
- **Advanced Risk Management**: Greeks-based risk controls and position limits
- **Comprehensive Analytics**: Options-specific performance metrics and Greeks P&L
- **SmartAPI Integration**: Direct integration with Angel One NFO segment

## Workflows
- **Full Pipeline**: End-to-end options trading pipeline
- **Live Trading**: Real-time market monitoring and trading
- **Training Pipeline**: Data preparation, strategy development, and AI training
- **Data Pipeline**: Data ingestion and feature engineering
- **Strategy Development**: Strategy generation, backtesting, and evolution
- **Options Research**: Data analysis and LLM interface
- **Multi-timeframe Analysis**: Analysis across different timeframes (1min, 3min, 5min, 15min)---
description: Repository Information Overview
alwaysApply: true
---

# NIFTY & BANK NIFTY Options Trading System

## Summary
Advanced AI-powered options trading system for Nifty 50 and Bank Nifty options with comprehensive backtesting, real-time signal generation, risk management, and automated execution capabilities. The system leverages machine learning, high-performance data processing, and advanced options strategies.

## Structure
- **agents/**: Options trading agents for various functions (monitoring, signals, risk, execution)
- **config/**: Configuration files for different system components
- **data/**: Storage for historical/live data, option chains, Greeks, volatility data
- **docs/**: Documentation including guides and API references
- **logs/**: System logs organized by date and component
- **tests/**: Test suite with unit and integration tests
- **utils/**: Utility modules for options pricing, data management, etc.
- **serena/**: Embedded subproject with its own structure and dependencies

## Language & Runtime
**Language**: Python 3.12+
**Build System**: Standard Python setuptools
**Package Manager**: pip
**Data Processing**: Polars + PyArrow for high-performance operations

## Dependencies
**Main Dependencies**:
- **Data Processing**: polars (≥0.20.0), pyarrow (≥15.0.0)
- **ML/AI**: lightgbm (≥4.3.0), torch (≥2.2.0), scikit-learn (≥1.4.0)
- **Options-Specific**: py_vollib (≥1.0.1), QuantLib-Python (≥1.32)
- **API Integration**: smartapi-python (≥1.4.8), websocket-client (≥1.7.0)
- **Web Framework**: fastapi (≥0.109.0), uvicorn (≥0.27.0)

**Development Dependencies**:
- pytest (≥8.0.0), black (≥24.0.0), flake8 (≥7.0.0), mypy (≥1.8.0)

## Build & Installation
```bash
# Basic installation
pip install -r requirements.txt

# For GPU acceleration (NVIDIA GPU with CUDA 11.8+)
# Uncomment GPU sections in requirements.txt
pip install -r requirements.txt

# Verify installation
python -c "import lightgbm, polars, torch, py_vollib; print('✅ Installation successful')"
```

## Testing
**Framework**: pytest with custom fixtures
**Test Location**: tests/ directory
**Naming Convention**: test_*.py files
**Configuration**: tests/conftest.py
**Run Command**:
```bash
pytest tests/
```

## Main Entry Points
**Main System**: main.py (OptionsSystemOrchestrator)
**Data Download**: download_historical_data.py, download_options_with_expiry_input.py
**Live Trading**: run_live_trading.py
**Sample Data**: generate_sample_data.py

## Key Features
- **Real-time Options Trading**: Live option chain monitoring and signal generation
- **AI-Powered Strategies**: ML-based options strategy selection and optimization
- **High Performance**: Built with Polars, PyArrow, and GPU acceleration
- **Advanced Risk Management**: Greeks-based risk controls and position limits
- **Comprehensive Analytics**: Options-specific performance metrics and Greeks P&L
- **SmartAPI Integration**: Direct integration with Angel One NFO segment

## Workflows
- **Full Pipeline**: End-to-end options trading pipeline
- **Live Trading**: Real-time market monitoring and trading
- **Training Pipeline**: Data preparation, strategy development, and AI training
- **Data Pipeline**: Data ingestion and feature engineering
- **Strategy Development**: Strategy generation, backtesting, and evolution
- **Options Research**: Data analysis and LLM interface
- **Multi-timeframe Analysis**: Analysis across different timeframes (1min, 3min, 5min, 15min)