#!/usr/bin/env python3
"""
System Status Monitor - Comprehensive System Health and Performance Monitoring

Features:
📊 1. Real-time System Status Updates (every 200s)
- Current balance tracking
- Used balance monitoring
- P&L estimation (real-time and closed trades)
- P&L percentage calculations
- Daily trade count tracking
- Active positions monitoring

💰 2. Portfolio Management
- Capital allocation tracking
- Risk exposure monitoring
- Position sizing analysis
- Margin utilization

🔔 3. Alert System
- Performance threshold alerts
- Risk limit notifications
- System health warnings
- Trading milestone notifications

📈 4. Performance Analytics
- Win/loss ratios
- Average trade duration
- Best/worst performing strategies
- Daily/weekly/monthly summaries
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import yaml
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class SystemStatus:
    """System status data structure"""
    timestamp: str
    current_balance: float
    used_balance: float
    available_balance: float
    total_pnl_real: float  # Based on closed trades only
    total_pnl_estimated: float  # Including open positions
    total_pnl_percentage: float
    daily_trades_completed: int
    daily_trades_target: int
    active_positions: int
    max_active_positions: int
    system_uptime: str
    trading_mode: str
    last_trade_time: Optional[str]
    daily_win_rate: float
    daily_best_trade: float
    daily_worst_trade: float
    memory_usage_mb: float
    cpu_usage_percent: float

@dataclass
class TradeRecord:
    """Individual trade record"""
    trade_id: str
    timestamp: str
    symbol: str
    action: str  # BUY/SELL
    quantity: int
    entry_price: float
    exit_price: Optional[float]
    pnl: Optional[float]
    status: str  # OPEN/CLOSED/CANCELLED
    strategy: str
    confidence: float

class SystemStatusMonitor:
    """Comprehensive system status monitoring"""
    
    def __init__(self, config_path: str = "config/system_status_config.yaml"):
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.is_running = False
        self.start_time = datetime.now()
        
        # Status tracking
        self.initial_balance = 100000.0  # Default initial balance
        self.current_balance = self.initial_balance
        self.used_balance = 0.0
        self.trades_today: List[TradeRecord] = []
        self.active_positions: List[TradeRecord] = []
        self.closed_trades_today: List[TradeRecord] = []
        
        # Performance tracking
        self.daily_pnl_real = 0.0
        self.daily_pnl_estimated = 0.0
        self.daily_trades_target = 50  # Default target
        
        # System metrics
        self.last_status_update = datetime.now()
        self.status_update_interval = 200  # seconds
        
        logger.info("📊 [INIT] System Status Monitor initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the system status monitor"""
        try:
            await self._load_config()
            
            # Extract initial parameters
            self.initial_balance = kwargs.get('initial_balance', 100000.0)
            self.current_balance = self.initial_balance
            self.daily_trades_target = kwargs.get('daily_trades_target', 50)
            
            logger.info("✅ [SUCCESS] System Status Monitor initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to initialize system status monitor: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from YAML file"""
        default_config = {
            'status_update_interval': 200,
            'daily_trades_target': 50,
            'alert_thresholds': {
                'max_daily_loss_percent': 5.0,
                'min_win_rate': 40.0,
                'max_drawdown_percent': 10.0,
                'memory_usage_mb': 1000,
                'cpu_usage_percent': 80.0
            },
            'notifications': {
                'enabled': True,
                'channels': ['console', 'file'],
                'milestone_alerts': True
            }
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    file_config = yaml.safe_load(f)
                    self.config = {**default_config, **file_config}
            except Exception as e:
                logger.warning(f"[WARNING] Failed to load config file, using defaults: {e}")
                self.config = default_config
        else:
            self.config = default_config
            logger.info("[CONFIG] Using default configuration")
    
    async def start(self, **kwargs) -> bool:
        """Start the system status monitor"""
        try:
            logger.info("🚀 [START] Starting System Status Monitor...")
            self.is_running = True
            
            # Start monitoring tasks
            self.monitoring_tasks = [
                asyncio.create_task(self._status_update_loop()),
                asyncio.create_task(self._performance_tracking_loop()),
                asyncio.create_task(self._system_health_monitoring())
            ]
            
            logger.info("✅ [SUCCESS] System Status Monitor started successfully")
            return True
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to start system status monitor: {e}")
            return False
    
    async def _status_update_loop(self):
        """Main status update loop - runs every 200 seconds"""
        while self.is_running:
            try:
                await self._generate_status_update()
                await asyncio.sleep(self.config['status_update_interval'])
            except Exception as e:
                logger.error(f"❌ [ERROR] Status update loop failed: {e}")
                await asyncio.sleep(30)  # Shorter retry interval
    
    async def _generate_status_update(self):
        """Generate comprehensive system status update"""
        try:
            # Calculate current metrics
            available_balance = self.current_balance - self.used_balance
            total_pnl_percentage = ((self.current_balance - self.initial_balance) / self.initial_balance) * 100
            
            # Calculate daily metrics
            daily_trades_completed = len(self.closed_trades_today)
            daily_win_rate = self._calculate_daily_win_rate()
            daily_best_trade = self._get_best_trade_today()
            daily_worst_trade = self._get_worst_trade_today()
            
            # System metrics
            uptime = str(datetime.now() - self.start_time).split('.')[0]
            memory_usage, cpu_usage = await self._get_system_metrics()
            
            # Create status object
            status = SystemStatus(
                timestamp=datetime.now().isoformat(),
                current_balance=self.current_balance,
                used_balance=self.used_balance,
                available_balance=available_balance,
                total_pnl_real=self.daily_pnl_real,
                total_pnl_estimated=self.daily_pnl_estimated,
                total_pnl_percentage=total_pnl_percentage,
                daily_trades_completed=daily_trades_completed,
                daily_trades_target=self.daily_trades_target,
                active_positions=len(self.active_positions),
                max_active_positions=self.config.get('max_active_positions', 5),
                system_uptime=uptime,
                trading_mode=getattr(self, 'trading_mode', 'PAPER'),
                last_trade_time=self._get_last_trade_time(),
                daily_win_rate=daily_win_rate,
                daily_best_trade=daily_best_trade,
                daily_worst_trade=daily_worst_trade,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage
            )
            
            # Display status update
            await self._display_status_update(status)
            
            # Check for alerts
            await self._check_alert_conditions(status)
            
            # Save status to file
            await self._save_status_to_file(status)
            
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to generate status update: {e}")
    
    async def _display_status_update(self, status: SystemStatus):
        """Display formatted status update"""
        logger.info("=" * 80)
        logger.info("📊 SYSTEM STATUS UPDATE")
        logger.info("=" * 80)
        logger.info(f"🕒 Timestamp: {status.timestamp}")
        logger.info(f"💰 Current Balance: ₹{status.current_balance:,.2f}")
        logger.info(f"💸 Used Balance: ₹{status.used_balance:,.2f}")
        logger.info(f"💵 Available Balance: ₹{status.available_balance:,.2f}")
        logger.info(f"📈 P&L (Real): ₹{status.total_pnl_real:,.2f}")
        logger.info(f"📊 P&L (Estimated): ₹{status.total_pnl_estimated:,.2f}")
        logger.info(f"📉 P&L %: {status.total_pnl_percentage:.2f}%")
        logger.info(f"🎯 Today Completed Trades: {status.daily_trades_completed}/{status.daily_trades_target}")
        logger.info(f"📋 Active Positions: {status.active_positions}/{status.max_active_positions}")
        logger.info(f"⏱️ System Uptime: {status.system_uptime}")
        logger.info(f"🎮 Trading Mode: {status.trading_mode}")
        logger.info(f"🏆 Daily Win Rate: {status.daily_win_rate:.1f}%")
        logger.info(f"💎 Best Trade Today: ₹{status.daily_best_trade:,.2f}")
        logger.info(f"💔 Worst Trade Today: ₹{status.daily_worst_trade:,.2f}")
        logger.info(f"🧠 Memory Usage: {status.memory_usage_mb:.1f} MB")
        logger.info(f"⚡ CPU Usage: {status.cpu_usage_percent:.1f}%")
        logger.info("=" * 80)

    def _calculate_daily_win_rate(self) -> float:
        """Calculate daily win rate"""
        if not self.closed_trades_today:
            return 0.0

        winning_trades = sum(1 for trade in self.closed_trades_today if trade.pnl and trade.pnl > 0)
        return (winning_trades / len(self.closed_trades_today)) * 100

    def _get_best_trade_today(self) -> float:
        """Get best trade P&L today"""
        if not self.closed_trades_today:
            return 0.0

        return max(trade.pnl for trade in self.closed_trades_today if trade.pnl)

    def _get_worst_trade_today(self) -> float:
        """Get worst trade P&L today"""
        if not self.closed_trades_today:
            return 0.0

        return min(trade.pnl for trade in self.closed_trades_today if trade.pnl)

    def _get_last_trade_time(self) -> Optional[str]:
        """Get timestamp of last trade"""
        if not self.trades_today:
            return None

        return max(trade.timestamp for trade in self.trades_today)

    async def _get_system_metrics(self) -> tuple[float, float]:
        """Get system memory and CPU usage"""
        try:
            import psutil
            memory_usage = psutil.virtual_memory().used / (1024 * 1024)  # MB
            cpu_usage = psutil.cpu_percent(interval=1)
            return memory_usage, cpu_usage
        except ImportError:
            logger.warning("[WARNING] psutil not available, using default system metrics")
            return 500.0, 25.0  # Default values
        except Exception as e:
            logger.error(f"[ERROR] Failed to get system metrics: {e}")
            return 0.0, 0.0

    async def _check_alert_conditions(self, status: SystemStatus):
        """Check for alert conditions and send notifications"""
        alerts = []
        thresholds = self.config['alert_thresholds']

        # Check daily loss threshold
        if status.total_pnl_percentage < -thresholds['max_daily_loss_percent']:
            alerts.append(f"🚨 Daily loss exceeded {thresholds['max_daily_loss_percent']}%: {status.total_pnl_percentage:.2f}%")

        # Check win rate threshold
        if status.daily_win_rate < thresholds['min_win_rate'] and status.daily_trades_completed >= 5:
            alerts.append(f"⚠️ Daily win rate below {thresholds['min_win_rate']}%: {status.daily_win_rate:.1f}%")

        # Check memory usage
        if status.memory_usage_mb > thresholds['memory_usage_mb']:
            alerts.append(f"🧠 High memory usage: {status.memory_usage_mb:.1f} MB")

        # Check CPU usage
        if status.cpu_usage_percent > thresholds['cpu_usage_percent']:
            alerts.append(f"⚡ High CPU usage: {status.cpu_usage_percent:.1f}%")

        # Check if approaching daily trade target
        if status.daily_trades_completed >= status.daily_trades_target * 0.8:
            alerts.append(f"🎯 Approaching daily trade target: {status.daily_trades_completed}/{status.daily_trades_target}")

        # Send alerts
        for alert in alerts:
            logger.warning(alert)
            await self._send_notification(alert)

    async def _send_notification(self, message: str):
        """Send notification through configured channels"""
        if not self.config['notifications']['enabled']:
            return

        try:
            # Console notification (already logged)

            # File notification
            if 'file' in self.config['notifications']['channels']:
                await self._save_alert_to_file(message)

            # TODO: Add other notification channels (Telegram, Email, etc.)

        except Exception as e:
            logger.error(f"[ERROR] Failed to send notification: {e}")

    async def _save_alert_to_file(self, message: str):
        """Save alert to file"""
        try:
            alerts_file = Path("data/alerts/system_alerts.log")
            alerts_file.parent.mkdir(parents=True, exist_ok=True)

            with open(alerts_file, 'a') as f:
                f.write(f"{datetime.now().isoformat()} - {message}\n")
        except Exception as e:
            logger.error(f"[ERROR] Failed to save alert to file: {e}")

    async def _save_status_to_file(self, status: SystemStatus):
        """Save status to JSON file"""
        try:
            status_file = Path("data/status/system_status.json")
            status_file.parent.mkdir(parents=True, exist_ok=True)

            with open(status_file, 'w') as f:
                json.dump(asdict(status), f, indent=2)
        except Exception as e:
            logger.error(f"[ERROR] Failed to save status to file: {e}")

    async def _performance_tracking_loop(self):
        """Performance tracking loop"""
        while self.is_running:
            try:
                await self._update_performance_metrics()
                await asyncio.sleep(60)  # Update every minute
            except Exception as e:
                logger.error(f"❌ [ERROR] Performance tracking failed: {e}")
                await asyncio.sleep(30)

    async def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            # Calculate real P&L from closed trades
            self.daily_pnl_real = sum(trade.pnl for trade in self.closed_trades_today if trade.pnl)

            # Calculate estimated P&L including open positions
            estimated_open_pnl = sum(self._estimate_position_pnl(pos) for pos in self.active_positions)
            self.daily_pnl_estimated = self.daily_pnl_real + estimated_open_pnl

            # Update current balance
            self.current_balance = self.initial_balance + self.daily_pnl_real

        except Exception as e:
            logger.error(f"[ERROR] Failed to update performance metrics: {e}")

    def _estimate_position_pnl(self, position: TradeRecord) -> float:
        """Estimate P&L for open position (placeholder)"""
        # TODO: Implement real-time P&L calculation based on current market prices
        return 0.0

    async def _system_health_monitoring(self):
        """System health monitoring loop"""
        while self.is_running:
            try:
                await self._check_system_health()
                await asyncio.sleep(120)  # Check every 2 minutes
            except Exception as e:
                logger.error(f"❌ [ERROR] System health monitoring failed: {e}")
                await asyncio.sleep(60)

    async def _check_system_health(self):
        """Check system health metrics"""
        try:
            # Check if agents are responsive
            # Check data freshness
            # Check disk space
            # Check network connectivity
            pass  # TODO: Implement detailed health checks
        except Exception as e:
            logger.error(f"[ERROR] System health check failed: {e}")

    # Public methods for external integration

    def add_trade(self, trade: TradeRecord):
        """Add a new trade record"""
        self.trades_today.append(trade)
        if trade.status == 'OPEN':
            self.active_positions.append(trade)
        elif trade.status == 'CLOSED':
            self.closed_trades_today.append(trade)
            # Remove from active positions if it was there
            self.active_positions = [pos for pos in self.active_positions if pos.trade_id != trade.trade_id]

    def update_trade(self, trade_id: str, updates: Dict[str, Any]):
        """Update an existing trade"""
        for trade in self.trades_today:
            if trade.trade_id == trade_id:
                for key, value in updates.items():
                    if hasattr(trade, key):
                        setattr(trade, key, value)

                # Handle status changes
                if 'status' in updates:
                    if updates['status'] == 'CLOSED':
                        if trade not in self.closed_trades_today:
                            self.closed_trades_today.append(trade)
                        self.active_positions = [pos for pos in self.active_positions if pos.trade_id != trade_id]
                break

    def update_balance(self, new_balance: float):
        """Update current balance"""
        self.current_balance = new_balance

    def update_used_balance(self, used_amount: float):
        """Update used balance"""
        self.used_balance = used_amount

    async def cleanup(self):
        """Cleanup the system status monitor"""
        try:
            logger.info("🛑 [CLEANUP] Stopping System Status Monitor...")
            self.is_running = False

            # Cancel all monitoring tasks
            if hasattr(self, 'monitoring_tasks'):
                for task in self.monitoring_tasks:
                    if not task.done():
                        task.cancel()

                # Wait for tasks to complete cancellation
                await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)

            logger.info("✅ [CLEANUP] System Status Monitor stopped successfully")
        except Exception as e:
            logger.error(f"❌ [ERROR] Error during cleanup: {e}")


# Example usage
async def main():
    """Example usage of System Status Monitor"""
    monitor = SystemStatusMonitor()

    try:
        await monitor.initialize(initial_balance=100000.0, daily_trades_target=50)
        await monitor.start()

        # Simulate some trades
        trade1 = TradeRecord(
            trade_id="T001",
            timestamp=datetime.now().isoformat(),
            symbol="NIFTY24800CE",
            action="BUY",
            quantity=1,
            entry_price=150.0,
            exit_price=None,
            pnl=None,
            status="OPEN",
            strategy="momentum",
            confidence=0.75
        )
        monitor.add_trade(trade1)

        # Keep running
        await asyncio.sleep(300)  # Run for 5 minutes

    except KeyboardInterrupt:
        logger.info("Monitor interrupted by user")
    finally:
        await monitor.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
