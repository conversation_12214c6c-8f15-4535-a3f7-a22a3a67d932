#!/usr/bin/env python3
"""
Test script to verify the performance improvements in the backtesting agent
"""

import asyncio
import time
import logging
from agents.options_backtesting_agent import OptionsBacktestingAgent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_performance():
    """Test the performance improvements"""
    try:
        logger.info("🚀 Testing Ultra High-Performance Options Backtesting Agent")
        
        # Create agent
        start_time = time.time()
        agent = OptionsBacktestingAgent()
        
        # Check optimization levels
        logger.info(f"Optimization Level: {agent._get_optimization_level()}")
        logger.info(f"Expected Performance Boost: {agent._estimate_performance_boost():.1f}x")
        
        # Initialize agent
        await agent.initialize()
        init_time = time.time() - start_time
        logger.info(f"Agent initialization time: {init_time:.3f} seconds")
        
        # Test with training pipeline parameters
        logger.info("Starting performance test with training pipeline...")
        test_start = time.time()
        
        success = await agent.start(
            from_date="2025-07-01",
            to_date="2025-07-19",
            skip_historical=True
        )
        
        test_time = time.time() - test_start
        
        if success:
            logger.info(f"✅ Performance test completed successfully in {test_time:.2f} seconds")
            logger.info(f"Performance stats: {agent.performance_stats}")
        else:
            logger.error("❌ Performance test failed")
        
        # Cleanup
        await agent.cleanup()
        
        total_time = time.time() - start_time
        logger.info(f"Total test time: {total_time:.2f} seconds")
        
        return success
        
    except Exception as e:
        logger.error(f"Performance test error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = asyncio.run(test_performance())
    if success:
        print("\n🎉 Performance optimization test PASSED!")
    else:
        print("\n❌ Performance optimization test FAILED!")
