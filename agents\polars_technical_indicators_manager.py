"""
Polars-based Technical Indicators Manager for Real-time Market Analysis

This module provides real-time technical indicator calculation using Polars and polars-talib
for efficient market regime detection and signal generation.

Key Features:
- Uses only Polars, PyArrow, and polars-talib (no external dependencies)
- Incremental data updates with efficient append operations
- Real-time market regime detection
- Separate handling for index vs option data
- Memory-efficient streaming data processing
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import threading

# Polars ecosystem only
import polars as pl
import polars_talib as plta
import aiofiles

logger = logging.getLogger(__name__)


class MarketRegime(Enum):
    """Market regime classification"""
    TRENDING_BULL = "trending_bull"
    TRENDING_BEAR = "trending_bear"
    SIDEWAYS_LOW_VOL = "sideways_low_vol"
    SIDEWAYS_HIGH_VOL = "sideways_high_vol"
    VOLATILE_UNCERTAIN = "volatile_uncertain"
    BREAKOUT = "breakout"


@dataclass
class MarketConditions:
    """Market conditions data structure"""
    regime: MarketRegime
    trend_strength: float
    volatility_level: float
    momentum_state: str
    support_level: float
    resistance_level: float
    rsi: float
    sma_20: float
    sma_50: float
    timestamp: datetime


class PolarsTechnicalIndicatorsManager:
    """
    Manages real-time technical indicators using Polars and polars-talib
    
    Uses efficient Polars operations for optimal performance in live trading
    """
    
    def __init__(self, config_path: str = "config/technical_indicators_config.yaml"):
        self.config_path = Path(config_path)
        self.data_path = Path("data")
        self.live_path = self.data_path / "live"
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Polars DataFrames for each underlying (in-memory time series)
        self.index_data = {
            'NIFTY': pl.DataFrame({
                'timestamp': [],
                'open': [],
                'high': [],
                'low': [],
                'close': [],
                'volume': []
            }, schema={
                'timestamp': pl.Datetime,
                'open': pl.Float64,
                'high': pl.Float64,
                'low': pl.Float64,
                'close': pl.Float64,
                'volume': pl.Float64
            }),
            'BANKNIFTY': pl.DataFrame({
                'timestamp': [],
                'open': [],
                'high': [],
                'low': [],
                'close': [],
                'volume': []
            }, schema={
                'timestamp': pl.Datetime,
                'open': pl.Float64,
                'high': pl.Float64,
                'low': pl.Float64,
                'close': pl.Float64,
                'volume': pl.Float64
            })
        }
        
        # Market conditions cache
        self.market_conditions = {}
        
        # Configuration
        self.max_rows = 1000  # Keep last 1000 data points for indicators
        self.min_rows_for_analysis = 50  # Minimum rows needed for reliable analysis
        
        logger.info("[INIT] Polars Technical Indicators Manager initialized")

    async def update_indicators(self, underlying: str, ohlc_data: Dict[str, Any]):
        """
        Update indicators with new OHLC data using Polars
        
        Args:
            underlying: NIFTY or BANKNIFTY
            ohlc_data: Dictionary containing OHLC data
        """
        try:
            with self.lock:
                if underlying not in self.index_data:
                    logger.warning(f"[WARNING] No data structure for {underlying}")
                    return
                
                # Create new row as Polars DataFrame
                new_row = pl.DataFrame({
                    'timestamp': [ohlc_data.get('timestamp', datetime.now())],
                    'open': [float(ohlc_data.get('open', 0))],
                    'high': [float(ohlc_data.get('high', 0))],
                    'low': [float(ohlc_data.get('low', 0))],
                    'close': [float(ohlc_data.get('close', 0))],
                    'volume': [float(ohlc_data.get('volume', 0))]
                })
                
                # Check if we need to align columns (after indicators are calculated)
                current_df = self.index_data[underlying]
                if len(current_df.columns) > 6:  # Has indicators
                    # Add missing indicator columns to new_row with null values
                    for col in current_df.columns:
                        if col not in new_row.columns:
                            new_row = new_row.with_columns(pl.lit(None).alias(col))

                    # Reorder columns to match current_df
                    new_row = new_row.select(current_df.columns)

                # Append new data efficiently
                self.index_data[underlying] = pl.concat([
                    current_df,
                    new_row
                ], how="vertical")

                # Keep only last N rows for memory efficiency
                if len(self.index_data[underlying]) > self.max_rows:
                    self.index_data[underlying] = self.index_data[underlying].tail(self.max_rows)
                
                # Calculate indicators if we have enough data
                if len(self.index_data[underlying]) >= self.min_rows_for_analysis:
                    await self._calculate_indicators(underlying)
                    await self._analyze_market_conditions(underlying)
                else:
                    logger.debug(f"[INDICATORS] {underlying}: Need {self.min_rows_for_analysis - len(self.index_data[underlying])} more rows for analysis")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to update indicators for {underlying}: {e}")

    async def _calculate_indicators(self, underlying: str):
        """Calculate technical indicators using polars-talib"""
        try:
            df = self.index_data[underlying]
            
            # Calculate indicators using polars-talib
            df_with_indicators = df.with_columns([
                # Trend indicators
                plta.sma(pl.col("close"), timeperiod=20).alias("sma_20"),
                plta.sma(pl.col("close"), timeperiod=50).alias("sma_50"),
                plta.ema(pl.col("close"), timeperiod=12).alias("ema_12"),
                plta.ema(pl.col("close"), timeperiod=26).alias("ema_26"),
                
                # Momentum indicators
                plta.rsi(pl.col("close"), timeperiod=14).alias("rsi_14"),
                
                # MACD
                plta.macd(pl.col("close"), fastperiod=12, slowperiod=26, signalperiod=9).alias("macd_data"),
                
                # Volatility indicators
                plta.atr(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("atr_14"),
                
                # Bollinger Bands
                plta.bbands(pl.col("close"), timeperiod=20, nbdevup=2, nbdevdn=2, matype=0).alias("bb_data")
            ])
            
            # Extract MACD components
            df_with_indicators = df_with_indicators.with_columns([
                pl.col("macd_data").struct.field("macd").alias("macd"),
                pl.col("macd_data").struct.field("macdsignal").alias("macd_signal"),
                pl.col("macd_data").struct.field("macdhist").alias("macd_hist"),
                
                # Extract Bollinger Bands components
                pl.col("bb_data").struct.field("upperband").alias("bb_upper"),
                pl.col("bb_data").struct.field("middleband").alias("bb_middle"),
                pl.col("bb_data").struct.field("lowerband").alias("bb_lower")
            ]).drop(["macd_data", "bb_data"])
            
            # Update the stored data with indicators
            self.index_data[underlying] = df_with_indicators
            
            # Get latest values for logging
            latest = df_with_indicators.tail(1).to_dicts()[0]
            close_price = latest.get('close', 0)
            rsi = latest.get('rsi_14')
            sma_20 = latest.get('sma_20')
            
            rsi_str = f"{rsi:.2f}" if rsi is not None else "N/A"
            sma_str = f"{sma_20:.2f}" if sma_20 is not None else "N/A"
            logger.debug(f"[INDICATORS] {underlying}: Close=₹{close_price:.2f}, RSI={rsi_str}, SMA20={sma_str}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate indicators for {underlying}: {e}")

    async def _analyze_market_conditions(self, underlying: str):
        """Analyze current market conditions based on indicators"""
        try:
            df = self.index_data[underlying]
            if len(df) == 0:
                return
            
            # Get latest values
            latest = df.tail(1).to_dicts()[0]
            
            current_price = latest.get('close', 0)
            current_rsi = latest.get('rsi_14', 50)
            current_sma_20 = latest.get('sma_20', 0)
            current_sma_50 = latest.get('sma_50', 0)
            current_atr = latest.get('atr_14', 0)
            
            # Skip analysis if key indicators are None
            if any(x is None for x in [current_rsi, current_sma_20, current_sma_50]):
                logger.debug(f"[REGIME] {underlying}: Waiting for indicators to stabilize")
                return
            
            # Determine trend strength
            trend_strength = 0.0
            if current_sma_20 > 0 and current_sma_50 > 0:
                if current_price > current_sma_20 > current_sma_50:
                    trend_strength = 0.8  # Strong bullish
                elif current_price > current_sma_20:
                    trend_strength = 0.4  # Mild bullish
                elif current_price < current_sma_20 < current_sma_50:
                    trend_strength = -0.8  # Strong bearish
                elif current_price < current_sma_20:
                    trend_strength = -0.4  # Mild bearish
            
            # Calculate volatility level (normalized ATR)
            volatility_level = 0.15  # Default
            if current_atr and current_price > 0:
                volatility_level = current_atr / current_price
            
            # Determine regime
            regime = self._determine_market_regime(current_rsi, trend_strength, volatility_level)
            
            # Determine momentum state
            momentum_state = "neutral"
            if current_rsi > 70:
                momentum_state = "overbought"
            elif current_rsi < 30:
                momentum_state = "oversold"
            elif current_rsi > 60:
                momentum_state = "bullish"
            elif current_rsi < 40:
                momentum_state = "bearish"
            
            # Create market conditions
            conditions = MarketConditions(
                regime=regime,
                trend_strength=trend_strength,
                volatility_level=volatility_level,
                momentum_state=momentum_state,
                support_level=current_sma_50 if current_sma_50 else current_price * 0.98,
                resistance_level=current_sma_20 if current_price < current_sma_20 else current_price * 1.02,
                rsi=current_rsi,
                sma_20=current_sma_20 if current_sma_20 else 0,
                sma_50=current_sma_50 if current_sma_50 else 0,
                timestamp=datetime.now()
            )
            
            self.market_conditions[underlying] = conditions
            
            logger.debug(f"[REGIME] {underlying}: {regime.value}, Trend: {trend_strength:.2f}, RSI: {current_rsi:.2f}, Vol: {volatility_level:.3f}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze market conditions for {underlying}: {e}")

    def _determine_market_regime(self, rsi: float, trend_strength: float, volatility_level: float) -> MarketRegime:
        """Determine market regime based on indicators"""
        try:
            # Strong trend conditions
            if abs(trend_strength) > 0.6:
                if trend_strength > 0:
                    return MarketRegime.TRENDING_BULL
                else:
                    return MarketRegime.TRENDING_BEAR
            
            # High volatility conditions
            if volatility_level > 0.03:  # 3% daily volatility
                if abs(trend_strength) < 0.2:
                    return MarketRegime.VOLATILE_UNCERTAIN
                else:
                    return MarketRegime.BREAKOUT
            
            # Sideways conditions
            if volatility_level > 0.015:  # 1.5% daily volatility
                return MarketRegime.SIDEWAYS_HIGH_VOL
            else:
                return MarketRegime.SIDEWAYS_LOW_VOL
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to determine market regime: {e}")
            return MarketRegime.SIDEWAYS_LOW_VOL

    def get_market_conditions(self, underlying: str) -> Optional[MarketConditions]:
        """Get current market conditions for an underlying"""
        return self.market_conditions.get(underlying)

    def get_indicator_values(self, underlying: str) -> Dict[str, Any]:
        """Get current indicator values for an underlying"""
        try:
            with self.lock:
                if underlying not in self.index_data or len(self.index_data[underlying]) == 0:
                    return {}
                
                # Get latest row
                latest = self.index_data[underlying].tail(1).to_dicts()[0]
                
                return {
                    'close': latest.get('close', 0),
                    'rsi_14': latest.get('rsi_14'),
                    'sma_20': latest.get('sma_20'),
                    'sma_50': latest.get('sma_50'),
                    'ema_12': latest.get('ema_12'),
                    'ema_26': latest.get('ema_26'),
                    'atr_14': latest.get('atr_14'),
                    'macd': latest.get('macd'),
                    'macd_signal': latest.get('macd_signal'),
                    'bb_upper': latest.get('bb_upper'),
                    'bb_lower': latest.get('bb_lower'),
                    'timestamp': latest.get('timestamp', datetime.now()).isoformat() if latest.get('timestamp') else datetime.now().isoformat(),
                    'data_points': len(self.index_data[underlying])
                }
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to get indicator values for {underlying}: {e}")
            return {}

    async def save_market_conditions(self):
        """Save current market conditions to file"""
        try:
            conditions_data = {}
            
            for underlying, conditions in self.market_conditions.items():
                if conditions:
                    conditions_data[underlying] = {
                        'regime': conditions.regime.value,
                        'trend_strength': conditions.trend_strength,
                        'volatility_level': conditions.volatility_level,
                        'momentum_state': conditions.momentum_state,
                        'support_level': conditions.support_level,
                        'resistance_level': conditions.resistance_level,
                        'rsi': conditions.rsi,
                        'sma_20': conditions.sma_20,
                        'sma_50': conditions.sma_50,
                        'timestamp': conditions.timestamp.isoformat()
                    }
            
            # Save to file
            self.live_path.mkdir(parents=True, exist_ok=True)
            output_path = self.live_path / "market_conditions.json"
            
            async with aiofiles.open(output_path, 'w') as f:
                await f.write(json.dumps(conditions_data, indent=2))
            
            logger.debug("[SAVE] Market conditions saved")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save market conditions: {e}")

    async def save_indicator_data(self, underlying: str):
        """Save current indicator data to parquet file"""
        try:
            if underlying not in self.index_data or len(self.index_data[underlying]) == 0:
                return
            
            # Create indicators directory
            indicators_path = self.live_path / "indicators"
            indicators_path.mkdir(parents=True, exist_ok=True)
            
            # Save as parquet for efficiency
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{underlying}_indicators_{timestamp_str}.parquet"
            filepath = indicators_path / filename
            
            # Save the DataFrame
            self.index_data[underlying].write_parquet(filepath)
            
            logger.debug(f"[SAVE] Saved {underlying} indicator data: {len(self.index_data[underlying])} rows")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save indicator data for {underlying}: {e}")

    def get_data_summary(self) -> Dict[str, Any]:
        """Get summary of current data state"""
        try:
            summary = {}
            
            for underlying in ['NIFTY', 'BANKNIFTY']:
                if underlying in self.index_data:
                    df = self.index_data[underlying]
                    conditions = self.market_conditions.get(underlying)
                    
                    summary[underlying] = {
                        'data_points': len(df),
                        'latest_price': df.tail(1)['close'].to_list()[0] if len(df) > 0 else 0,
                        'regime': conditions.regime.value if conditions else 'unknown',
                        'trend_strength': conditions.trend_strength if conditions else 0,
                        'rsi': conditions.rsi if conditions else 50,
                        'ready_for_analysis': len(df) >= self.min_rows_for_analysis
                    }
            
            return summary
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get data summary: {e}")
            return {}
