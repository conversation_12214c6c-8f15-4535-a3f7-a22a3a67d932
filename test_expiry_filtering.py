#!/usr/bin/env python3
"""
Test script to verify expiry filtering functionality
Tests that only instruments with expiry dates from .env file are downloaded
"""

import asyncio
import logging
import os
from datetime import datetime
from pathlib import Path
import json
import tempfile
import shutil

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_expiry_filtering():
    """Test that expiry filtering works correctly"""
    
    logger.info("[TEST] Starting expiry filtering test...")
    
    try:
        # Check if .env file exists and has expiry dates
        from dotenv import load_dotenv
        load_dotenv()
        
        nifty_expiry = os.getenv("NIFTY_EXPIRY")
        banknifty_expiry = os.getenv("BANKNIFTY_EXPIRY")
        
        if not nifty_expiry or not banknifty_expiry:
            logger.error("[TEST] NIFTY_EXPIRY and BANKNIFTY_EXPIRY must be set in .env file")
            logger.error("[TEST] Example: NIFTY_EXPIRY=14AUG2025")
            logger.error("[TEST] Example: BANKNIFTY_EXPIRY=28AUG2025")
            return False
        
        logger.info(f"[TEST] Testing with expiry dates from .env:")
        logger.info(f"[TEST] NIFTY_EXPIRY: {nifty_expiry}")
        logger.info(f"[TEST] BANKNIFTY_EXPIRY: {banknifty_expiry}")
        
        # Import the data ingestion agent
        import sys
        sys.path.append(str(Path(__file__).parent))
        
        from agents.options_data_ingestion_agent import OptionsDataIngestionAgent
        
        # Create agent instance
        agent = OptionsDataIngestionAgent()
        
        # Initialize configuration (this loads expiry dates from .env)
        await agent._load_config()
        
        logger.info(f"[TEST] Agent loaded expiry dates:")
        logger.info(f"[TEST] NIFTY: {agent.expiry_dates_raw.get('NIFTY', 'Not set')}")
        logger.info(f"[TEST] BANKNIFTY: {agent.expiry_dates_raw.get('BANKNIFTY', 'Not set')}")
        
        # Test instrument filtering without actual API connection
        # We'll create mock instruments to test the filtering logic
        mock_instruments = create_mock_instruments(nifty_expiry, banknifty_expiry)
        
        logger.info(f"[TEST] Created {len(mock_instruments)} mock instruments for testing")
        
        # Test the filtering logic
        filtered_instruments = []
        for instrument in mock_instruments:
            if (instrument.get('exch_seg') == 'NFO' and
                instrument.get('instrumenttype') == 'OPTIDX' and
                instrument.get('name') in ['NIFTY', 'BANKNIFTY']):

                # Apply the same filtering logic as in the agent
                expiry_str = instrument.get('expiry')
                if expiry_str and expiry_str != '':
                    try:
                        # Parse expiry date (format: 24DEC2025)
                        expiry_date = datetime.strptime(expiry_str, '%d%b%Y').date()
                        underlying = instrument.get('name')
                        
                        # Check if this expiry matches the one specified in .env file
                        if underlying in agent.expiry_dates_raw:
                            target_expiry_raw = agent.expiry_dates_raw[underlying]
                            try:
                                # Parse target expiry from .env (format: 14AUG2025)
                                target_expiry_date = datetime.strptime(target_expiry_raw, '%d%b%Y').date()
                                
                                # Only include instruments with the exact expiry date from .env
                                if expiry_date == target_expiry_date:
                                    filtered_instruments.append(instrument)
                                    logger.debug(f"[TEST] Including {underlying} instrument with expiry {expiry_str}")
                                else:
                                    logger.debug(f"[TEST] Skipping {underlying} instrument with expiry {expiry_str} (target: {target_expiry_raw})")
                                    
                            except ValueError as ve:
                                logger.warning(f"[TEST] Invalid target expiry format for {underlying}: {target_expiry_raw}")
                                continue
                        else:
                            logger.debug(f"[TEST] Skipping {underlying} - not configured in .env file")
                            
                    except ValueError:
                        logger.debug(f"[TEST] Failed to parse expiry date: {expiry_str}")
                        continue
        
        # Analyze results
        nifty_filtered = [inst for inst in filtered_instruments if inst.get('name') == 'NIFTY']
        banknifty_filtered = [inst for inst in filtered_instruments if inst.get('name') == 'BANKNIFTY']
        
        logger.info(f"[TEST] Filtering results:")
        logger.info(f"[TEST] Total filtered instruments: {len(filtered_instruments)}")
        logger.info(f"[TEST] NIFTY instruments: {len(nifty_filtered)}")
        logger.info(f"[TEST] BANKNIFTY instruments: {len(banknifty_filtered)}")
        
        # Verify that all filtered instruments have the correct expiry dates
        success = True
        
        for instrument in nifty_filtered:
            if instrument.get('expiry') != nifty_expiry:
                logger.error(f"[TEST] ❌ NIFTY instrument has wrong expiry: {instrument.get('expiry')} (expected: {nifty_expiry})")
                success = False
        
        for instrument in banknifty_filtered:
            if instrument.get('expiry') != banknifty_expiry:
                logger.error(f"[TEST] ❌ BANKNIFTY instrument has wrong expiry: {instrument.get('expiry')} (expected: {banknifty_expiry})")
                success = False
        
        # Check that we have some instruments (assuming the expiry dates are valid)
        if len(filtered_instruments) == 0:
            logger.warning("[TEST] ⚠️ No instruments found - this might indicate invalid expiry dates in .env")
            logger.warning("[TEST] Please check that the expiry dates in .env file are valid and available")
        
        if success and len(filtered_instruments) > 0:
            logger.info("[TEST] ✅ Expiry filtering test PASSED")
            logger.info("[TEST] All filtered instruments have the correct expiry dates from .env file")
            return True
        elif success and len(filtered_instruments) == 0:
            logger.warning("[TEST] ⚠️ Expiry filtering logic is correct but no instruments found")
            logger.warning("[TEST] This might be due to invalid expiry dates in .env file")
            return True  # Logic is correct, just no matching instruments
        else:
            logger.error("[TEST] ❌ Expiry filtering test FAILED")
            logger.error("[TEST] Some instruments have incorrect expiry dates")
            return False
        
    except Exception as e:
        logger.error(f"[TEST] Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_mock_instruments(nifty_expiry: str, banknifty_expiry: str):
    """Create mock instruments for testing"""
    mock_instruments = []
    
    # Create instruments with correct expiry dates
    for underlying in ['NIFTY', 'BANKNIFTY']:
        expiry = nifty_expiry if underlying == 'NIFTY' else banknifty_expiry
        
        # Create some instruments with the correct expiry
        for strike in [24000, 24050, 24100]:
            for option_type in ['CE', 'PE']:
                mock_instruments.append({
                    'token': f'token_{underlying}_{strike}_{option_type}',
                    'symbol': f'{underlying}{expiry}{strike}{option_type}',
                    'name': underlying,
                    'expiry': expiry,
                    'strike': str(strike * 100),  # Strike in paise
                    'lotsize': '50' if underlying == 'NIFTY' else '25',
                    'instrumenttype': 'OPTIDX',
                    'exch_seg': 'NFO'
                })
    
    # Create some instruments with wrong expiry dates (should be filtered out)
    wrong_expiries = ['15AUG2025', '29AUG2025', '05SEP2025']
    for underlying in ['NIFTY', 'BANKNIFTY']:
        for wrong_expiry in wrong_expiries:
            for strike in [24000, 24050]:
                for option_type in ['CE', 'PE']:
                    mock_instruments.append({
                        'token': f'token_{underlying}_{strike}_{option_type}_wrong',
                        'symbol': f'{underlying}{wrong_expiry}{strike}{option_type}',
                        'name': underlying,
                        'expiry': wrong_expiry,
                        'strike': str(strike * 100),
                        'lotsize': '50' if underlying == 'NIFTY' else '25',
                        'instrumenttype': 'OPTIDX',
                        'exch_seg': 'NFO'
                    })
    
    # Add some non-options instruments (should be filtered out)
    for underlying in ['NIFTY', 'BANKNIFTY']:
        mock_instruments.append({
            'token': f'token_{underlying}_fut',
            'symbol': f'{underlying}{nifty_expiry if underlying == "NIFTY" else banknifty_expiry}FUT',
            'name': underlying,
            'expiry': nifty_expiry if underlying == 'NIFTY' else banknifty_expiry,
            'strike': '0',
            'lotsize': '50' if underlying == 'NIFTY' else '25',
            'instrumenttype': 'FUTIDX',  # Future, not option
            'exch_seg': 'NFO'
        })
    
    return mock_instruments

async def test_env_file_validation():
    """Test that .env file has valid expiry date formats"""
    logger.info("[TEST] Testing .env file expiry date formats...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        nifty_expiry = os.getenv("NIFTY_EXPIRY")
        banknifty_expiry = os.getenv("BANKNIFTY_EXPIRY")
        
        if not nifty_expiry or not banknifty_expiry:
            logger.error("[TEST] ❌ NIFTY_EXPIRY and BANKNIFTY_EXPIRY must be set in .env file")
            return False
        
        # Test date format parsing
        try:
            nifty_date = datetime.strptime(nifty_expiry, '%d%b%Y')
            logger.info(f"[TEST] ✅ NIFTY_EXPIRY format is valid: {nifty_expiry} -> {nifty_date.date()}")
        except ValueError:
            logger.error(f"[TEST] ❌ NIFTY_EXPIRY has invalid format: {nifty_expiry} (expected: DDMMMYYYY, e.g., 14AUG2025)")
            return False
        
        try:
            banknifty_date = datetime.strptime(banknifty_expiry, '%d%b%Y')
            logger.info(f"[TEST] ✅ BANKNIFTY_EXPIRY format is valid: {banknifty_expiry} -> {banknifty_date.date()}")
        except ValueError:
            logger.error(f"[TEST] ❌ BANKNIFTY_EXPIRY has invalid format: {banknifty_expiry} (expected: DDMMMYYYY, e.g., 28AUG2025)")
            return False
        
        # Check if dates are in the future
        today = datetime.now().date()
        if nifty_date.date() < today:
            logger.warning(f"[TEST] ⚠️ NIFTY_EXPIRY is in the past: {nifty_expiry}")
        
        if banknifty_date.date() < today:
            logger.warning(f"[TEST] ⚠️ BANKNIFTY_EXPIRY is in the past: {banknifty_expiry}")
        
        logger.info("[TEST] ✅ .env file expiry date validation PASSED")
        return True
        
    except Exception as e:
        logger.error(f"[TEST] .env file validation failed: {e}")
        return False

if __name__ == "__main__":
    async def run_all_tests():
        logger.info("Starting Expiry Filtering Tests...")
        
        # Test 1: .env file validation
        test1_result = await test_env_file_validation()
        
        # Test 2: Expiry filtering logic
        test2_result = await test_expiry_filtering()
        
        # Overall result
        if test1_result and test2_result:
            logger.info("🎉 All expiry filtering tests PASSED!")
            logger.info("✅ The system will now only download instruments with expiry dates from .env file")
        else:
            logger.error("❌ Some expiry filtering tests FAILED!")
            logger.error("Please check your .env file and expiry date configuration")
    
    asyncio.run(run_all_tests())
