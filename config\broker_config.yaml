# Broker Integration Configuration
# Configuration for real and paper trading brokers

# SmartAPI Configuration (for real trading)
smartapi:
  # API credentials (keep these secure!)
  api_key: ""                    # Your SmartAPI API key
  client_code: ""                # Your client code
  password: ""                   # Your password
  totp_secret: ""                # TOTP secret for 2FA (optional)
  
  # Connection settings
  base_url: "https://apiconnect.angelbroking.com"
  timeout: 30                    # Request timeout in seconds
  max_retries: 3                 # Maximum retry attempts
  retry_delay: 1                 # Delay between retries in seconds
  
  # Rate limiting
  requests_per_second: 10        # Maximum requests per second
  burst_limit: 50               # Burst request limit
  
  # Order settings
  default_exchange: "NFO"        # Default exchange for options
  default_product_type: "INTRADAY"  # Default product type
  default_validity: "DAY"        # Default order validity
  
  # Risk settings
  max_order_value: 100000        # Maximum order value in INR
  max_daily_orders: 100          # Maximum orders per day
  position_size_limit: 10        # Maximum position size (lots)

# Paper Trading Configuration
paper_trading:
  # Initial account settings
  initial_balance: 100000.0      # Starting balance in INR
  leverage: 1.0                  # Leverage multiplier
  
  # Simulation settings
  fill_delay_ms: 100             # Simulated order fill delay
  slippage_bps: 5                # Slippage in basis points
  commission_per_trade: 20       # Commission per trade in INR
  
  # Market simulation
  market_hours_only: true        # Only allow trading during market hours
  weekend_trading: false         # Allow weekend trading
  
  # Price simulation
  use_real_prices: true          # Use real market prices when available
  price_volatility: 0.02         # Price volatility for simulation
  
  # Order execution
  immediate_fill: true           # Fill orders immediately
  partial_fills: false           # Allow partial order fills
  
  # Account tracking
  track_pnl: true               # Track P&L
  track_positions: true         # Track positions
  save_trade_history: true      # Save trade history to file

# Broker Selection
broker_selection:
  # Trading mode determines which broker to use
  # Options: "paper", "real"
  default_mode: "paper"
  
  # Fallback settings
  fallback_to_paper: true        # Fallback to paper if real broker fails
  
  # Validation settings
  validate_orders: true          # Validate orders before placement
  check_balance: true           # Check balance before orders
  check_positions: true         # Check position limits

# Order Management
order_management:
  # Order validation
  min_order_value: 100           # Minimum order value in INR
  max_order_value: 500000        # Maximum order value in INR
  
  # Position limits
  max_positions: 20              # Maximum number of positions
  max_position_value: 100000     # Maximum value per position
  
  # Risk checks
  pre_trade_checks: true         # Enable pre-trade risk checks
  position_limit_checks: true    # Check position limits
  margin_checks: true           # Check margin requirements
  
  # Order types allowed
  allowed_order_types:
    - "MARKET"
    - "LIMIT"
    - "STOP_LOSS"
    - "STOP_LOSS_MARKET"
  
  # Product types allowed
  allowed_product_types:
    - "INTRADAY"
    - "DELIVERY"
    - "CARRYFORWARD"

# Market Data
market_data:
  # Real-time data settings
  enable_real_time: true         # Enable real-time market data
  subscription_symbols: []       # Symbols to subscribe to
  
  # Data sources
  primary_source: "smartapi"     # Primary data source
  fallback_source: "mock"        # Fallback data source
  
  # WebSocket settings
  websocket_enabled: true        # Enable WebSocket for real-time data
  websocket_reconnect: true      # Auto-reconnect WebSocket
  max_reconnect_attempts: 5      # Maximum reconnection attempts
  
  # Data quality
  validate_data: true           # Validate incoming data
  filter_invalid_prices: true   # Filter out invalid prices
  
  # Caching
  cache_market_data: true       # Cache market data
  cache_duration_seconds: 60    # Cache duration

# Risk Management Integration
risk_management:
  # Position monitoring
  monitor_positions: true        # Monitor position changes
  position_update_interval: 5    # Position update interval in seconds
  
  # P&L monitoring
  monitor_pnl: true             # Monitor P&L changes
  pnl_update_interval: 10       # P&L update interval in seconds
  
  # Risk alerts
  enable_risk_alerts: true      # Enable risk-based alerts
  alert_thresholds:
    max_loss_percent: 5.0       # Alert if loss exceeds 5%
    max_position_size: 50000    # Alert if position size exceeds limit
    low_margin_percent: 20.0    # Alert if margin falls below 20%

# Logging and Monitoring
logging:
  # Log levels
  broker_log_level: "INFO"      # Broker operation log level
  order_log_level: "INFO"       # Order operation log level
  
  # Log files
  broker_log_file: "logs/broker.log"
  order_log_file: "logs/orders.log"
  trade_log_file: "logs/trades.log"
  
  # Log rotation
  max_log_size_mb: 100          # Maximum log file size
  backup_count: 5               # Number of backup log files
  
  # Audit trail
  maintain_audit_trail: true    # Maintain detailed audit trail
  audit_log_file: "logs/audit.log"

# Performance Monitoring
performance:
  # Latency monitoring
  monitor_latency: true         # Monitor order latency
  latency_threshold_ms: 1000    # Latency alert threshold
  
  # Throughput monitoring
  monitor_throughput: true      # Monitor order throughput
  throughput_window_seconds: 60 # Throughput measurement window
  
  # Error monitoring
  monitor_errors: true          # Monitor error rates
  error_threshold_percent: 5.0  # Error rate alert threshold
  
  # Performance logging
  log_performance_metrics: true # Log performance metrics
  performance_log_interval: 300 # Performance log interval in seconds

# Backup and Recovery
backup:
  # Data backup
  backup_enabled: true          # Enable data backup
  backup_interval_hours: 6      # Backup interval
  backup_location: "backups/broker"  # Backup directory
  
  # Recovery settings
  auto_recovery: true           # Enable automatic recovery
  recovery_timeout_seconds: 30  # Recovery timeout
  
  # State persistence
  persist_orders: true          # Persist order state
  persist_positions: true       # Persist position state
  state_file: "data/broker_state.json"  # State file location

# Testing and Development
testing:
  # Test mode settings
  test_mode: false              # Enable test mode
  mock_responses: false         # Use mock API responses
  
  # Simulation settings
  simulate_network_delays: false  # Simulate network delays
  simulate_api_errors: false     # Simulate API errors
  error_rate_percent: 1.0        # Simulated error rate
  
  # Development features
  debug_mode: false             # Enable debug mode
  verbose_logging: false        # Enable verbose logging
  save_api_responses: false     # Save API responses for debugging

# Security Settings
security:
  # Credential security
  encrypt_credentials: false    # Encrypt stored credentials
  credential_timeout_hours: 24  # Credential timeout
  
  # API security
  validate_ssl: true           # Validate SSL certificates
  use_secure_connections: true  # Use secure connections only
  
  # Access control
  ip_whitelist: []             # IP address whitelist (empty = allow all)
  rate_limiting: true          # Enable rate limiting
  
  # Audit and compliance
  log_all_requests: false      # Log all API requests
  compliance_mode: false       # Enable compliance mode
