#!/usr/bin/env python3
"""
Options LLM Interface Agent - Natural Language Options Trading Interface

Features:
🧠 1. Natural Language Processing
- Options strategy queries
- Market analysis requests
- Risk assessment questions
- Trading decision support

📊 2. Local LLM Integration
- Ollama model integration
- Multiple model selection
- Context-aware responses
- Options domain expertise

⚡ 3. Real-time Insights
- Live market commentary
- Strategy recommendations
- Risk alerts explanation
- Performance analysis

🎯 4. Interactive Features
- Chat-based interface
- Voice command support
- Automated reporting
- Educational content
"""

import asyncio
import logging
import polars as pl
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import yaml
from langchain_ollama import OllamaLLM
from langchain_core.messages import HumanMessage, SystemMessage

logger = logging.getLogger(__name__)

class OptionsLLMInterfaceAgent:
    """Options LLM Interface Agent for natural language trading interface"""
    
    def __init__(self, config_path: str = "config/options_llm_interface_config.yaml"):
        self.config_path = Path(config_path)
        self.config = None
        self.is_running = False
        self.llm_models: Dict[str, OllamaLLM] = {}
        self.strategy_metadata: Dict[str, Any] = {}
        
        logger.info("[INIT] Options LLM Interface Agent initialized")
    
    async def initialize(self):
        """Initialize the agent"""
        try:
            await self._load_config()
            self._load_ollama_models()
            await self._load_strategy_metadata()
            logger.info("[SUCCESS] Options LLM Interface Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from YAML file"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Config file not found: {self.config_path}")
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        logger.info(f"[CONFIG] Configuration loaded from {self.config_path}")

    def _load_ollama_models(self):
        """Load Ollama models based on configuration"""
        logger.info("[LLM] Starting to load Ollama models...")
        for purpose, model_name in self.config['llm_models'].items():
            try:
                logger.info(f"[LLM] Attempting to load model '{model_name}' for {purpose}...")
                self.llm_models[purpose] = OllamaLLM(model=model_name, timeout=30)
                logger.info(f"[LLM] Successfully loaded Ollama model '{model_name}' for {purpose}")
            except Exception as e:
                logger.error(f"[LLM ERROR] Could not load Ollama model '{model_name}' for {purpose}. Please ensure Ollama is running and the model is downloaded. Error: {e}")
                # If a critical model like 'general' fails to load, we might want to raise an exception
                if purpose == 'general':
                    raise RuntimeError(f"Critical Ollama model '{model_name}' for 'general' purpose failed to load.")
        logger.info(f"[LLM] Finished loading {len(self.llm_models)} Ollama models")

    async def _load_strategy_metadata(self):
        """Load strategy metadata from the YAML file specified in config"""
        strategy_yaml_path = self.config['data_paths']['strategy_yaml']
        if not Path(strategy_yaml_path).exists():
            raise FileNotFoundError(f"Strategy YAML file not found: {strategy_yaml_path}")
        with open(strategy_yaml_path, 'r', encoding='utf-8') as f:
            self.strategy_metadata = yaml.safe_load(f)
        logger.info(f"[CONFIG] Strategy metadata loaded from {strategy_yaml_path}")

    async def _invoke_llm(self, prompt: str, model_purpose: str = 'general', system_message: Optional[str] = None) -> str:
        """
        Invoke the appropriate LLM model with a given prompt.
        Args:
            prompt (str): The user's prompt.
            model_purpose (str): The purpose of the model (e.g., 'general', 'code', 'analysis').
            system_message (Optional[str]): An optional system message to guide the LLM.
        Returns:
            str: The LLM's response.
        """
        model = self.llm_models.get(model_purpose)
        if not model:
            logger.error(f"[LLM] No Ollama model configured for purpose: {model_purpose}. Using 'general' model.")
            model = self.llm_models.get('general')
            if not model:
                raise ValueError(f"No 'general' Ollama model configured. Cannot proceed.")

        messages = []
        if system_message:
            messages.append(SystemMessage(content=system_message))
        messages.append(HumanMessage(content=prompt))

        try:
            logger.info(f"[LLM] Invoking {model_purpose} model with prompt: '{prompt[:50]}...'")
            response = await model.ainvoke(messages)
            logger.info(f"[LLM] Successfully got response from {model_purpose} model")
            return response
        except Exception as e:
            logger.error(f"[LLM] Error invoking LLM for {model_purpose} with prompt '{prompt[:50]}...': {e}")
            return f"Error: Could not process your request. {e}"
    
    async def _explain_trade_signal(self, 
                                    trade_signal: Dict[str, Any], 
                                    confidence_score: float, 
                                    market_regime: Dict[str, Any],
                                    feature_snapshot: Dict[str, Any]) -> str:
        """
        Converts raw trade signals into natural-language summaries using strategy metadata and confidence scores.
        Args:
            trade_signal (Dict[str, Any]): The raw trade signal details.
            confidence_score (float): The confidence score of the signal.
            market_regime (Dict[str, Any]): Current market regime details.
            feature_snapshot (Dict[str, Any]): Snapshot of relevant features (e.g., IV Rank, RSI).
        Returns:
            str: A natural-language summary of the trade signal.
        """
        strategy_id = trade_signal.get('strategy_id', 'unknown_strategy')
        action = trade_signal.get('action', 'trade')
        instrument = trade_signal.get('instrument', 'N/A')
        strike = trade_signal.get('strike', 'N/A')
        option_type = trade_signal.get('option_type', 'N/A').upper()

        strategy_name = "Unknown Strategy"
        strategy_description = "No description available."
        
        # Search for strategy metadata
        for category in self.strategy_metadata.values():
            if isinstance(category, dict):
                for strat_key, strat_details in category.items():
                    if strat_key == strategy_id:
                        strategy_name = strat_details.get('name', strategy_id)
                        strategy_description = strat_details.get('description', 'No description available.')
                        break
            if strategy_name != "Unknown Strategy":
                break

        iv_rank = feature_snapshot.get('iv_rank', 'N/A')
        rsi = feature_snapshot.get('rsi', 'N/A')
        regime_type = market_regime.get('type', 'unknown')
        regime_volatility = market_regime.get('volatility', 'unknown')

        system_message = (
            "You are an expert financial analyst. Your task is to convert raw trade signals "
            "into concise, natural-language summaries for traders. Focus on clarity, "
            "strategy context, confidence, and relevant market conditions. "
            "Use emojis to make the explanation more engaging. 📈📉📊"
        )

        prompt = (
            f"Explain the following trade signal:\n\n"
            f"Action: {action}\n"
            f"Instrument: {instrument} {strike}{option_type}\n"
            f"Strategy ID: {strategy_id} (Name: {strategy_name})\n"
            f"Confidence Score: {confidence_score:.2f}\n"
            f"Market Regime: Type: {regime_type}, Volatility: {regime_volatility}\n"
            f"Feature Snapshot: IV Rank: {iv_rank}, RSI: {rsi}\n\n"
            f"Strategy Description: {strategy_description}\n\n"
            f"Provide a summary similar to: 'We’re buying 47800CE because Strategy #12 (Opening Range Breakout) was triggered with high confidence in a bullish low-volatility regime. IV Rank is 42 and RSI is at 67, suggesting a strong momentum continuation setup.'"
        )

        response = await self._invoke_llm(prompt, model_purpose='signal_explanation', system_message=system_message)
        return response

    async def _interpret_strategy(self, query: str) -> str:
        """
        Answers questions about strategies using strategy definitions, performance logs, and evolution history.
        Args:
            query (str): The natural language query about strategies.
        Returns:
            str: The LLM's answer to the query.
        """
        system_message = (
            "You are an expert in options trading strategies. Your task is to answer questions "
            "about strategies based on provided metadata, performance logs, and evolution history. "
            "Be concise, accurate, and helpful. Use emojis to enhance readability. 📚💡📈"
        )

        # Convert strategy_metadata to a readable string for the LLM
        strategy_data_str = json.dumps(self.strategy_metadata, indent=2)

        # Placeholder for performance logs and evolution history
        performance_logs_summary = "No recent performance logs available."
        evolution_history_summary = "No strategy evolution history available."

        prompt = (
            f"Answer the following question about options trading strategies:\n\n"
            f"Query: \"{query}\"\n\n"
            f"Available Strategy Definitions (YAML converted to JSON):\n{strategy_data_str}\n\n"
            f"Recent Performance Logs Summary: {performance_logs_summary}\n\n"
            f"Strategy Evolution History Summary: {evolution_history_summary}\n\n"
            f"Based on the above information, provide a comprehensive answer. "
            f"If the information is not directly available, state that and suggest what kind of data would be needed."
        )

        response = await self._invoke_llm(prompt, model_purpose='strategy_interpretation', system_message=system_message)
        return response

    async def _query_performance(self, query: str) -> str:
        """
        Answers questions about performance by retrieving and summarizing logs from the Performance Analysis Agent.
        Args:
            query (str): The natural language query about performance.
        Returns:
            str: The LLM's answer to the query.
        """
        system_message = (
            "You are an expert in analyzing trading performance. Your task is to answer questions "
            "about performance based on provided logs. Summarize key metrics and identify trends. "
            "Use emojis to make the explanation more engaging. 📈📉📊💰"
        )

        performance_logs_path = Path(self.config['data_paths']['performance_logs'])
        performance_data_summary = "No performance logs found."
        
        if performance_logs_path.exists() and any(performance_logs_path.iterdir()):
            performance_data_summary = f"Performance logs are available in {performance_logs_path}. (Actual data processing not yet implemented)."

        prompt = (
            f"Answer the following question about trading performance:\n\n"
            f"Query: \"{query}\"\n\n"
            f"Available Performance Data: {performance_data_summary}\n\n"
            f"Based on the above information, provide a comprehensive answer. "
            f"If the information is not directly available, state that and suggest what kind of data would be needed."
        )

        response = await self._invoke_llm(prompt, model_purpose='performance_query', system_message=system_message)
        return response

    async def _generate_market_commentary(self, market_data: Dict[str, Any], risk_flags: List[str]) -> str:
        """
        Auto-generates a market brief and explains risk flags.
        Args:
            market_data (Dict[str, Any]): Current market conditions (e.g., from market_summary.json).
            risk_flags (List[str]): Active risk flags.
        Returns:
            str: A natural-language market brief and risk commentary.
        """
        system_message = (
            "You are an expert market commentator and risk analyst. Your task is to generate "
            "concise and informative market briefs, and explain any active risk flags. "
            "Focus on clarity, market outlook, and risk implications. Use emojis. 📰🚨📊"
        )

        market_regime = market_data.get('market_regime', 'unknown')
        volatility_regime = market_data.get('volatility_regime', 'unknown')
        timestamp = market_data.get('timestamp', 'N/A')

        risk_commentary = "No active risk flags. Market conditions are stable. ✅"
        if risk_flags:
            risk_commentary = "🚨 Active Risk Flags: " + ", ".join(risk_flags) + ". Trading paused due to risk thresholds being reached. Please review the risk management dashboard for details. 🛑"

        prompt = (
            f"Generate a market brief and risk commentary based on the following:\n\n"
            f"Timestamp: {timestamp}\n"
            f"Market Regime: {market_regime}\n"
            f"Volatility Regime: {volatility_regime}\n"
            f"Active Risk Flags: {', '.join(risk_flags) if risk_flags else 'None'}\n\n"
            f"Example market brief: 'Market opened flat today but quickly trended upward. India VIX dropped to 10.8. We are currently in a bullish low-IV regime, ideal for momentum and breakout strategies.'\n"
            f"Example risk explanation: 'Trading paused due to 3 consecutive SL hits in under 1 hour. Risk threshold reached.'\n\n"
            f"Combine these into a coherent commentary."
        )

        response = await self._invoke_llm(prompt, model_purpose='regime_risk_commentary', system_message=system_message)
        return response

    async def _interpret_market_conditions(self, market_data: Dict[str, Any]) -> str:
        """
        Interpret current market conditions and provide insights.
        Args:
            market_data (Dict[str, Any]): Current market data and conditions.
        Returns:
            str: Natural language interpretation of market conditions.
        """
        system_message = (
            "You are an expert market analyst specializing in options trading. "
            "Analyze the provided market data and provide clear, actionable insights "
            "about current market conditions, volatility regime, and trading opportunities. "
            "Be concise and focus on what matters for options traders. Use emojis. 📊📈📉"
        )

        # Extract key market metrics
        market_regime = market_data.get('market_regime', 'unknown')
        volatility_regime = market_data.get('volatility_regime', 'unknown')
        timestamp = market_data.get('timestamp', 'N/A')
        underlying_data = market_data.get('underlying_data', {})

        # Build context for analysis
        nifty_data = underlying_data.get('NIFTY', {})
        banknifty_data = underlying_data.get('BANKNIFTY', {})

        prompt = (
            f"Analyze the current market conditions based on the following data:\n\n"
            f"Timestamp: {timestamp}\n"
            f"Market Regime: {market_regime}\n"
            f"Volatility Regime: {volatility_regime}\n"
            f"NIFTY Data: {json.dumps(nifty_data, indent=2) if nifty_data else 'No data available'}\n"
            f"BANKNIFTY Data: {json.dumps(banknifty_data, indent=2) if banknifty_data else 'No data available'}\n\n"
            f"Provide a brief market interpretation covering:\n"
            f"1. Current market sentiment and direction\n"
            f"2. Volatility environment and implications\n"
            f"3. Recommended trading approach for options\n"
            f"4. Key levels to watch\n\n"
            f"Keep it concise and actionable for options traders."
        )

        response = await self._invoke_llm(prompt, model_purpose='general', system_message=system_message)
        return response

    async def _debug_trade_signal(self, query: str) -> str:
        """
        Accepts a prompt like "Why was 47300PE trade taken and why did it fail?" and analyzes
        strategy logic, market conditions, signal confidence, SL/TP level correctness,
        and execution latency/slippage. Combines logs from Signal Generation Agent,
        Execution Agent, and Performance Agent.
        Args:
            query (str): The natural language query for debugging a trade.
        Returns:
            str: A detailed analysis of the trade.
        """
        system_message = (
            "You are an expert trade debugging and audit agent. Your task is to analyze "
            "trade failures or unexpected outcomes by examining strategy logic, market conditions, "
            "signal confidence, stop-loss/take-profit levels, and execution details. "
            "Provide a comprehensive, root-cause analysis. Use emojis. 🐛🔍🛠️"
        )

        signal_logs_path = Path(self.config['data_paths']['signal_generation_logs'])
        execution_logs_path = Path(self.config['data_paths']['execution_logs'])
        performance_logs_path = Path(self.config['data_paths']['performance_logs'])

        signal_data_summary = "No signal logs found."
        if signal_logs_path.exists() and any(signal_logs_path.iterdir()):
            signal_data_summary = f"Signal logs are available in {signal_logs_path}. (Actual data processing not yet implemented)."

        execution_data_summary = "No execution logs found."
        if execution_logs_path.exists() and any(execution_logs_path.iterdir()):
            execution_data_summary = f"Execution logs are available in {execution_logs_path}. (Actual data processing not yet implemented)."

        performance_data_summary = "No performance logs found."
        if performance_logs_path.exists() and any(performance_logs_path.iterdir()):
            performance_data_summary = f"Performance logs are available in {performance_logs_path}. (Actual data processing not yet implemented)."

        prompt = (
            f"Analyze the following trade debugging query:\n\n"
            f"Query: \"{query}\"\n\n"
            f"Contextual Data:\n"
            f"Strategy Definitions: {json.dumps(self.strategy_metadata, indent=2)}\n"
            f"Signal Generation Logs Summary: {signal_data_summary}\n"
            f"Execution Logs Summary: {execution_data_summary}\n"
            f"Performance Logs Summary: {performance_data_summary}\n\n"
            f"Provide a detailed explanation covering strategy logic, market conditions, "
            f"signal confidence, SL/TP levels, and execution factors. "
            f"If specific data is missing, state that clearly."
        )

        response = await self._invoke_llm(prompt, model_purpose='signal_debugging', system_message=system_message)
        return response

    async def _provide_strategy_evolution_guidance(self, 
                                                   strategy_id: str, 
                                                   performance_metrics: Dict[str, Any], 
                                                   market_conditions: Dict[str, Any]) -> str:
        """
        Summarizes strategy performance degradation and suggests evolution prompts.
        Args:
            strategy_id (str): The ID of the strategy.
            performance_metrics (Dict[str, Any]): Key performance metrics (e.g., Sharpe Ratio, Max Drawdown).
            market_conditions (Dict[str, Any]): Current or recent market conditions.
        Returns:
            str: Guidance for strategy evolution.
        """
        system_message = (
            "You are an expert in algorithmic trading strategy evolution. Your task is to "
            "analyze strategy performance degradation and suggest actionable evolution prompts. "
            "Focus on identifying root causes and proposing concrete improvements. Use emojis. 🧬📈💡"
        )

        strategy_name = self.strategy_metadata.get(strategy_id, {}).get('name', strategy_id)
        sharpe_ratio = performance_metrics.get('sharpe_ratio', 'N/A')
        max_drawdown = performance_metrics.get('max_drawdown', 'N/A')
        win_rate = performance_metrics.get('win_rate', 'N/A')
        market_regime = market_conditions.get('market_regime', 'unknown')
        volatility_regime = market_conditions.get('volatility_regime', 'unknown')

        evolution_history_summary = "No strategy evolution history available."
        evolution_history_path = Path(self.config['data_paths']['evolution_history'])
        if evolution_history_path.exists() and any(evolution_history_path.iterdir()):
            evolution_history_summary = f"Evolution history logs are available in {evolution_history_path}. (Actual data processing not yet implemented)."

        prompt = (
            f"Provide strategy evolution guidance for '{strategy_name}' ({strategy_id}) based on the following:\n\n"
            f"Current Performance Metrics: Sharpe Ratio: {sharpe_ratio}, Max Drawdown: {max_drawdown}, Win Rate: {win_rate}\n"
            f"Recent Market Conditions: Regime: {market_regime}, Volatility: {volatility_regime}\n"
            f"Evolution History Summary: {evolution_history_summary}\n\n"
            f"Summarize any performance degradation (e.g., 'Strat_014’s Sharpe dropped below 0.5 in the last 3 weeks in volatile conditions.').\n"
            f"Then, suggest concrete evolution prompts (e.g., 'Consider disabling it or adjusting SL buffer.', 'What if we add an IV filter to strat_014?', 'Can we test replacing RSI with ADX in strat_009?')."
        )

        response = await self._invoke_llm(prompt, model_purpose='strategy_evolution', system_message=system_message)
        return response

    async def _convert_nl_to_json(self, query: str) -> Dict[str, Any]:
        """
        Converts natural language queries to structured backend actions (JSON).
        Args:
            query (str): The natural language query.
        Returns:
            Dict[str, Any]: A JSON object representing the structured backend action.
        """
        system_message = (
            "You are a highly accurate natural language to JSON converter for trading actions. "
            "Your task is to parse a user's query and convert it into a structured JSON object "
            "that can be used to filter a Polars DataFrame or trigger a specific backend action. "
            "Identify the action, filters, and any relevant parameters. "
            "Ensure the output is valid JSON. 🤖➡️📄"
        )

        prompt = (
            f"Convert the following natural language query into a JSON object representing a backend action:\n\n"
            f"Query: \"{query}\"\n\n"
            f"Examples:\n"
            f"\"Show me trades that had confidence below 0.6 and were profitable\"\n"
            f"-> {{ \"action\": \"filter_trades\", \"filters\": [{{ \"field\": \"confidence\", \"operator\": \"lt\", \"value\": 0.6 }}, {{ \"field\": \"profitable\", \"operator\": \"eq\", \"value\": true }}], \"output\": \"summary\" }}\n\n"
            f"\"Get performance for strat_009 last month\"\n"
            f"-> {{ \"action\": \"get_performance\", \"strategy_id\": \"strat_009\", \"timeframe\": \"last_month\", \"output\": \"summary\" }}\n\n"
            f"\"Disable strat_015\"\n"
            f"-> {{ \"action\": \"update_strategy_status\", \"strategy_id\": \"strat_015\", \"status\": \"disabled\" }}\n\n"
            f"Provide only the JSON object in your response, no additional text."
        )

        response_str = await self._invoke_llm(prompt, model_purpose='nl_to_json', system_message=system_message)
        try:
            json_response = json.loads(response_str)
            return json_response
        except json.JSONDecodeError as e:
            logger.error(f"[LLM] Failed to parse JSON response from NL to JSON conversion: {e}. Raw response: {response_str}")
            return {"error": "Failed to parse JSON response", "raw_response": response_str}

    async def _interactive_mode_feedback(self, prompt: str) -> str:
        """
        Accepts "What would you do here?" prompts and suggests actions.
        Args:
            prompt (str): The user's interactive query.
        Returns:
            str: Suggested actions or insights.
        """
        system_message = (
            "You are an advanced AI agent capable of simulating rational trading behavior. "
            "Respond to 'What would you do here?' prompts by suggesting optimal actions, "
            "considering market conditions, strategy rules, and risk. "
            "Provide clear, actionable advice. Use emojis. 🧠💬💡"
        )

        # In a real scenario, this would involve more complex logic,
        # potentially integrating with live market data, strategy backtesting, etc.
        # For now, it's a direct LLM invocation.
        context_data = {
            "strategy_metadata": self.strategy_metadata,
            # Add other relevant real-time data here (e.g., current market snapshot, portfolio, open positions)
        }

        full_prompt = (
            f"The user is asking for guidance in an interactive trading scenario:\n\n"
            f"User's situation/question: \"{prompt}\"\n\n"
            f"Current context (simplified): {json.dumps(context_data, indent=2)}\n\n"
            f"Suggest what actions to take or provide insights. "
            f"Examples: 'Avoid this trade due to low PCR and weakening momentum', "
            f"'Use strat_013 if RSI recovers above 60 in next candle'."
        )

        response = await self._invoke_llm(full_prompt, model_purpose='interactive_feedback', system_message=system_message)
        return response

    async def _rewrite_json_summary(self, json_log: Dict[str, Any]) -> str:
        """
        Takes backend logs (JSON) and rewrites them as human-readable summaries.
        Args:
            json_log (Dict[str, Any]): The raw JSON log from the backend.
        Returns:
            str: A human-readable summary.
        """
        system_message = (
            "You are a log summarization agent. Your task is to take raw JSON backend logs "
            "and rewrite them into concise, human-readable natural language summaries for traders. "
            "Focus on key information like strategy, confidence, ROI, and reason for outcome. "
            "Use emojis to make it engaging. 📝✨📖"
        )

        prompt = (
            f"Rewrite the following JSON log into a human-readable summary:\n\n"
            f"JSON Log: {json.dumps(json_log, indent=2)}\n\n"
            f"Example:\n"
            f"{{ \"strategy\": \"strat_007\", \"confidence\": 0.52, \"roi\": -14.8, \"reason\": \"SL hit within 4 minutes\" }}\n"
            f"-> \"Strategy 7 triggered a weak-confidence trade that hit SL in 4 minutes. Entry was premature in a choppy market.\"\n\n"
            f"Provide only the human-readable summary, no additional text."
        )

        response = await self._invoke_llm(prompt, model_purpose='json_rewriter', system_message=system_message)
        return response

    async def _provide_education_guidance(self, query: str) -> str:
        """
        Provides educational support and explanations for traders.
        Args:
            query (str): The educational query (e.g., "What is Sharpe Ratio?").
        Returns:
            str: A comprehensive explanation.
        """
        system_message = (
            "You are an educational support system for traders, powered by a local LLM. "
            "Your task is to explain financial concepts, trading metrics, and AI model behaviors "
            "in a clear, concise, and easy-to-understand manner. Use analogies and examples where helpful. "
            "Use emojis to make learning fun! 🎓💡📚"
        )

        prompt = (
            f"Explain the following concept to a trader:\n\n"
            f"Query: \"{query}\"\n\n"
            f"Provide a detailed explanation. If it's a trading metric, explain its calculation and significance. "
            f"If it's an AI model behavior, explain the underlying logic simply."
        )

        response = await self._invoke_llm(prompt, model_purpose='education', system_message=system_message)
        return response
    
    async def start(self, **kwargs) -> bool:
        """Start the LLM interface agent"""
        try:
            logger.info("[START] Starting Options LLM Interface Agent...")
            self.is_running = True

            # Start LLM interface with timeout protection
            tasks = [
                self._process_queries(),
                self._generate_insights(),
                self._provide_recommendations(),
                self._explain_decisions()
            ]

            logger.info("[START] Starting background tasks...")
            await asyncio.gather(*tasks, return_exceptions=True)

            return True
        except asyncio.CancelledError:
            logger.info("[START] Agent start was cancelled")
            return False
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def _process_queries(self):
        """Process natural language queries"""
        logger.info("[LLM] Starting query processing task...")
        iteration_count = 0
        max_iterations = 10  # Limit iterations for testing

        while self.is_running and iteration_count < max_iterations:
            try:
                iteration_count += 1
                logger.info(f"[LLM] Processing user queries... (iteration {iteration_count}/{max_iterations})")

                # For now, just demonstrate that the task is running
                # In a real implementation, this would process actual user queries from a queue
                logger.info("[LLM] No active queries to process. Waiting...")

                await asyncio.sleep(10)  # Check for new queries
            except asyncio.CancelledError:
                logger.info("[LLM] Query processing task cancelled.")
                break # Exit the loop on cancellation
            except Exception as e:
                logger.error(f"[ERROR] Query processing failed: {e}")

        logger.info(f"[LLM] Query processing task completed after {iteration_count} iterations")
    
    async def _generate_insights(self):
        """Generate market insights"""
        logger.info("[LLM] Starting insight generation task...")
        iteration_count = 0
        max_iterations = 3  # Limit iterations for testing

        while self.is_running and iteration_count < max_iterations:
            try:
                iteration_count += 1
                logger.info(f"[LLM] Generating market insights... (iteration {iteration_count}/{max_iterations})")
                await asyncio.sleep(5)  # Shorter sleep for testing
            except asyncio.CancelledError:
                logger.info("[LLM] Insight generation task cancelled.")
                break # Exit the loop on cancellation
            except Exception as e:
                logger.error(f"[ERROR] Insight generation failed: {e}")

        logger.info(f"[LLM] Insight generation task completed after {iteration_count} iterations")

    async def _provide_recommendations(self):
        """Provide trading recommendations"""
        logger.info("[LLM] Starting recommendation task...")
        iteration_count = 0
        max_iterations = 2  # Limit iterations for testing

        while self.is_running and iteration_count < max_iterations:
            try:
                iteration_count += 1
                logger.info(f"[LLM] Providing recommendations... (iteration {iteration_count}/{max_iterations})")
                await asyncio.sleep(5)  # Shorter sleep for testing
            except asyncio.CancelledError:
                logger.info("[LLM] Recommendation generation task cancelled.")
                break # Exit the loop on cancellation
            except Exception as e:
                logger.error(f"[ERROR] Recommendation generation failed: {e}")

        logger.info(f"[LLM] Recommendation task completed after {iteration_count} iterations")

    async def _explain_decisions(self):
        """Explain trading decisions"""
        logger.info("[LLM] Starting decision explanation task...")
        iteration_count = 0
        max_iterations = 5  # Limit iterations for testing

        while self.is_running and iteration_count < max_iterations:
            try:
                iteration_count += 1
                logger.info(f"[LLM] Explaining decisions... (iteration {iteration_count}/{max_iterations})")
                await asyncio.sleep(3)  # Shorter sleep for testing
            except asyncio.CancelledError:
                logger.info("[LLM] Decision explanation task cancelled.")
                break # Exit the loop on cancellation
            except Exception as e:
                logger.error(f"[ERROR] Decision explanation failed: {e}")

        logger.info(f"[LLM] Decision explanation task completed after {iteration_count} iterations")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options LLM Interface Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options LLM Interface Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

async def interactive_mode(agent):
    """Interactive mode for testing LLM functionality"""
    logger.info("🎮 Interactive Mode - Type 'quit' to exit")
    logger.info("Available commands:")
    logger.info("  education <question> - Ask educational questions")
    logger.info("  strategy <question> - Ask about strategies")
    logger.info("  debug <question> - Debug trade signals")
    logger.info("  feedback <question> - Get interactive feedback")
    logger.info("  json <query> - Convert natural language to JSON")
    logger.info("  quit - Exit interactive mode")

    while True:
        try:
            user_input = input("\n💬 Enter command: ").strip()
            if user_input.lower() == 'quit':
                break

            parts = user_input.split(' ', 1)
            if len(parts) < 2:
                logger.info("❌ Please provide a command and question")
                continue

            command, question = parts

            if command == 'education':
                response = await agent._provide_education_guidance(question)
                logger.info(f"📚 Education: {response}")
            elif command == 'strategy':
                response = await agent._interpret_strategy(question)
                logger.info(f"🎯 Strategy: {response}")
            elif command == 'debug':
                response = await agent._debug_trade_signal(question)
                logger.info(f"🐛 Debug: {response}")
            elif command == 'feedback':
                response = await agent._interactive_mode_feedback(question)
                logger.info(f"💡 Feedback: {response}")
            elif command == 'json':
                response = await agent._convert_nl_to_json(question)
                logger.info(f"📄 JSON: {response}")
            else:
                logger.info("❌ Unknown command. Use: education, strategy, debug, feedback, json, or quit")

        except KeyboardInterrupt:
            break
        except Exception as e:
            logger.error(f"❌ Error: {e}")

    logger.info("👋 Exiting interactive mode")

# Example usage
async def main():
    import sys

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    agent = OptionsLLMInterfaceAgent()
    try:
        logger.info("Starting agent initialization...")
        success = await agent.initialize()
        if not success:
            logger.error("Failed to initialize agent")
            return

        logger.info("Agent initialized successfully!")

        # Check if we're in test mode
        if len(sys.argv) > 1 and sys.argv[1] == "--test":
            logger.info("Running in test mode - performing comprehensive functionality tests...")

            # Test 1: Education guidance
            logger.info("🧪 Test 1: Education guidance")
            test_response = await agent._provide_education_guidance("What is a call option?")
            logger.info(f"✅ Education response: {test_response[:100]}...")

            # Test 2: Trade signal explanation
            logger.info("🧪 Test 2: Trade signal explanation")
            trade_signal = {'strategy_id': 'long_call', 'action': 'buying', 'instrument': 'BANKNIFTY', 'strike': 47800, 'option_type': 'CE'}
            confidence = 0.85
            market_regime = {'type': 'bullish', 'volatility': 'low'}
            feature_snapshot = {'iv_rank': 42, 'rsi': 67}
            explanation = await agent._explain_trade_signal(trade_signal, confidence, market_regime, feature_snapshot)
            logger.info(f"✅ Trade signal explanation: {explanation[:100]}...")

            # Test 3: Strategy interpretation
            logger.info("🧪 Test 3: Strategy interpretation")
            strategy_query = "What is the long_call strategy?"
            interpretation = await agent._interpret_strategy(strategy_query)
            logger.info(f"✅ Strategy interpretation: {interpretation[:100]}...")

            # Test 4: Market commentary
            logger.info("🧪 Test 4: Market commentary")
            market_data = {"timestamp": "2025-07-22 18:05:00", "market_regime": "bullish", "volatility_regime": "low-IV"}
            risk_flags = ["3_consecutive_SL_hits"]
            commentary = await agent._generate_market_commentary(market_data, risk_flags)
            logger.info(f"✅ Market commentary: {commentary[:100]}...")

            # Test 5: Natural language to JSON conversion
            logger.info("🧪 Test 5: Natural language to JSON conversion")
            nl_query = "Show me trades that had confidence below 0.6 and were profitable"
            json_action = await agent._convert_nl_to_json(nl_query)
            logger.info(f"✅ NL to JSON conversion: {json_action}")

            logger.info("🎉 All tests completed successfully!")
        elif len(sys.argv) > 1 and sys.argv[1] == "--interactive":
            logger.info("Running in interactive mode...")
            await interactive_mode(agent)
        else:
            logger.info("Starting agent in full mode...")
            await agent.start()

    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
