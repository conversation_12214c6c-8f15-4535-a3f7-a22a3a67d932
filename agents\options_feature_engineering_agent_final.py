﻿#!/usr/bin/env python3
"""
Options Feature Engineering Agent - Greeks, Volatility & Technical Features

Features:
ðŸ“Š 1. Options Greeks Calculation
- Delta, Gamma, Theta, Vega, Rho calculations
- Greeks sensitivity analysis
- Portfolio Greeks aggregation
- Greeks-based risk metrics

ðŸ“ˆ 2. Volatility Modeling
- Implied volatility calculation
- Historical volatility analysis
- Volatility surface construction
- Volatility smile/skew analysis

âš¡ 3. Technical Features
- Options flow indicators
- Put-Call ratio analysis
- Open interest analysis
- Volume-weighted metrics

ðŸŽ¯ 4. High-Performance Processing
- Vectorized Greeks calculations
- Polars + PyArrow optimization
- GPU-accelerated computations
- Memory-efficient processing
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import aiofiles
from dataclasses import dataclass
import json
import math

# Options pricing libraries
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
    from py_vollib.black_scholes.implied_volatility import implied_volatility
except ImportError:
    logger.warning("py_vollib not installed. Using fallback calculations.")

# Use polars-talib for technical indicators
import polars_talib as ta

logger = logging.getLogger(__name__)

@dataclass
class GreeksData:
    """Greeks calculation results"""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    implied_vol: float

@dataclass
class VolatilityMetrics:
    """Volatility analysis results"""
    historical_vol: float
    implied_vol: float
    vol_rank: float
    vol_percentile: float
    vol_skew: float

class OptionsFeatureEngineeringAgent:
    """
    Options Feature Engineering Agent for calculating Greeks, volatility, and technical features
    
    Handles:
    - Options Greeks calculations (Delta, Gamma, Theta, Vega, Rho)
    - Implied and historical volatility analysis
    - Volatility surface construction
    - Options flow and sentiment indicators
    - Technical analysis on underlying assets
    """
    
    def __init__(self, config_path: str = "config/options_feature_engineering_config.yaml"):
        """Initialize Options Feature Engineering Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths for multi-timeframe processing
        self.data_path = Path("data")
        self.historical_path = self.data_path / "historical"
        self.live_path = self.data_path / "live"
        self.features_path = self.data_path / "features"
        self.greeks_path = self.data_path / "greeks"
        self.volatility_path = self.data_path / "volatility"

        # Multi-timeframe support
        self.timeframes = ["1min", "3min", "5min", "15min"]

        # Create directories for each timeframe
        for timeframe in self.timeframes:
            (self.features_path / timeframe).mkdir(parents=True, exist_ok=True)
            (self.greeks_path / timeframe).mkdir(parents=True, exist_ok=True)
            (self.volatility_path / timeframe).mkdir(parents=True, exist_ok=True)
        
        # Risk-free rate (can be updated from market data)
        self.risk_free_rate = 0.06  # 6% annual
        
        logger.info("[INIT] Options Feature Engineering Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            # Load configuration
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            logger.info("[SUCCESS] Options Feature Engineering Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from file"""
        try:
            # Default configuration
            self.config = {
                'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
                'lookback_days': 30,
                'volatility_window': 20,
                'risk_free_rate': 0.06,
                'chunk_size': 10000
            }
            logger.info("[CONFIG] Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise
    
    async def start(self, **kwargs) -> bool:
        """Enhanced start method with comprehensive feature engineering"""
        try:
            logger.info("[START] Starting Enhanced Options Feature Engineering Agent...")

            self.is_running = True

            # Extract date parameters if provided (for training pipeline)
            from_date = kwargs.get('from_date')
            to_date = kwargs.get('to_date')
            
            if from_date and to_date:
                logger.info("[MODE] Training pipeline mode - processing historical data")
                # Process historical data with comprehensive features
                success = await self._process_comprehensive_features(from_date, to_date)
                return success
            else:
                logger.info("[MODE] Live mode - processing real-time features")
                # Process features for each underlying and timeframe
                for underlying in self.config['underlying_symbols']:
                    for timeframe in self.timeframes:
                        await self._process_timeframe_features(underlying, timeframe)

                logger.info("[SUCCESS] Multi-timeframe feature engineering completed")
                return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _process_comprehensive_features(self, from_date: str, to_date: str) -> bool:
        """Process comprehensive features for training pipeline"""
        try:
            logger.info("[COMPREHENSIVE] Starting comprehensive feature engineering...")
            
            # Process features for each underlying and timeframe
            for underlying in self.config['underlying_symbols']:
                logger.info(f"[COMPREHENSIVE] Processing {underlying} features...")
                
                for timeframe in self.timeframes:
                    logger.info(f"[COMPREHENSIVE] Processing {timeframe} features for {underlying}...")
                    
                    # Load historical data
                    historical_data = await self._load_historical_data(underlying, timeframe)
                    
                    if historical_data is None or historical_data.height == 0:
                        logger.warning(f"[WARNING] No {timeframe} data found for {underlying}")
                        continue
                    
                    # Load index data for this underlying
                    index_data = await self._load_index_data(underlying, timeframe)
                    
                    # Calculate comprehensive features
                    features = await self._calculate_comprehensive_features(
                        historical_data, index_data, underlying, timeframe
                    )
                    
                    if features is not None:
                        # Save features with proper naming
                        await self._save_comprehensive_features(features, underlying, timeframe)
                        logger.info(f"[SUCCESS] Processed {features.height} feature records for {underlying} {timeframe}")
            
            logger.info("[SUCCESS] Comprehensive feature engineering completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Comprehensive feature engineering failed: {e}")
            return False
    
    async def _load_historical_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load historical data for comprehensive feature engineering"""
        try:
            # Look for historical data files
            data_path = self.historical_path / timeframe
            
            # Try different patterns for options data
            patterns = [
                f"{underlying}_*_{timeframe.upper()}_*.parquet",
                f"{underlying}_*_*.parquet",
                f"*{underlying}*.parquet"
            ]
            
            all_files = []
            for pattern in patterns:
                files = list(data_path.glob(pattern))
                all_files.extend(files)
            
            if not all_files:
                logger.warning(f"[LOAD] No historical {timeframe} data found for {underlying}")
                return None
            
            # Load and combine all files
            dataframes = []
            for file in all_files:
                try:
                    df = pl.read_parquet(file)
                    if 'underlying' in df.columns:
                        df = df.filter(pl.col('underlying') == underlying)
                    dataframes.append(df)
                except Exception as e:
                    logger.warning(f"[LOAD] Failed to load {file}: {e}")
                    continue
            
            if not dataframes:
                return None
            
            # Combine all dataframes
            combined_df = pl.concat(dataframes)
            logger.info(f"[LOAD] Loaded {combined_df.height} historical records for {underlying} {timeframe}")
            return combined_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load historical data: {e}")
            return None
    
    async def _load_index_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load index data for the underlying"""
        try:
            data_path = self.historical_path / timeframe
            
            # Look for index data files
            pattern = f"Index_{underlying}_{timeframe.upper()}_*.parquet"
            files = list(data_path.glob(pattern))
            
            if not files:
                # Try alternative patterns
                pattern = f"Index_{underlying}_*.parquet"
                files = list(data_path.glob(pattern))
            
            if not files:
                logger.warning(f"[LOAD] No index data found for {underlying}")
                return None
            
            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            df = pl.read_parquet(latest_file)
            
            logger.info(f"[LOAD] Loaded {df.height} index records for {underlying}")
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load index data: {e}")
            return None
    
    async def _calculate_comprehensive_features(self, options_data: pl.DataFrame, index_data: Optional[pl.DataFrame], 
                                              underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Calculate comprehensive features based on ml-agent-feature.txt"""
        try:
            logger.info(f"[FEATURES] Calculating comprehensive features for {underlying} {timeframe}...")
            
            # Start with options data
            features_df = options_data.clone()
            
            # 1. Core Market Features - Index Rate Impact Features
            if index_data is not None:
                features_df = await self._add_index_rate_features(features_df, index_data)
            
            # 2. Market Condition Classification Features
            features_df = await self._add_market_condition_features(features_df, index_data)
            
            # 3. Time-Based Features
            features_df = await self._add_time_based_features(features_df, timeframe)
            
            # 4. Option-Specific Features
            features_df = await self._add_option_specific_features(features_df, underlying)
            
            # 5. Greeks and Volatility Features
            features_df = await self._add_greeks_volatility_features(features_df)
            
            # 6. Market Microstructure Features
            features_df = await self._add_microstructure_features(features_df)
            
            # 7. Strategy Performance Features
            features_df = await self._add_strategy_performance_features(features_df)
            
            # 8. External Factors
            features_df = await self._add_external_factors_features(features_df, index_data)
            
            logger.info(f"[SUCCESS] Calculated {features_df.width} features for {underlying} {timeframe}")
            return features_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate comprehensive features: {e}")
            return None
    
    async def _add_index_rate_features(self, df: pl.DataFrame, index_data: Optional[pl.DataFrame]) -> pl.DataFrame:
        """Add Index Rate Impact Features"""
        try:
            # Interest Rate Sensitivity (using proxy values)
            df = df.with_columns([
                pl.lit(0.06).alias('risk_free_rate'),  # RBI repo rate proxy
                pl.lit(7.2).alias('ten_year_yield'),   # 10-year G-sec yield proxy
                pl.lit(0.5).alias('credit_spread')     # Banking sector credit spread proxy
            ])
            
            if index_data is not None:
                # Calculate index volatility as proxy for rate sensitivity
                index_returns = index_data.with_columns([
                    (pl.col('close').pct_change()).alias('returns')
                ])
                
                index_vol = index_returns.select([
                    pl.col('returns').std().alias('index_volatility')
                ]).item(0, 'index_volatility')
                
                df = df.with_columns([
                    pl.lit(index_vol if index_vol else 0.02).alias('index_volatility')
                ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add index rate features: {e}")
            return df
    
    async def _add_market_condition_features(self, df: pl.DataFrame, index_data: Optional[pl.DataFrame]) -> pl.DataFrame:
        """Add Market Condition Classification Features"""
        try:
            # Volatility Regimes
            df = df.with_columns([
                # Calculate rolling volatility
                (pl.col('close').pct_change().rolling_std(window_size=20) * 100).alias('rolling_volatility_20'),
                (pl.col('close').pct_change().rolling_std(window_size=5) * 100).alias('rolling_volatility_5')
            ])
            
            # Market Phase Detection
            df = df.with_columns([
                # Simple trend detection using moving averages
                pl.col('close').rolling_mean(window_size=20).alias('sma_20'),
                pl.col('close').rolling_mean(window_size=50).alias('sma_50'),
                pl.col('close').rolling_mean(window_size=200).alias('sma_200')
            ])
            
            # Market phase classification
            df = df.with_columns([
                pl.when(pl.col('close') > pl.col('sma_20'))
                .when(pl.col('sma_20') > pl.col('sma_50'))
                .then(pl.lit('bull'))
                .when(pl.col('close') < pl.col('sma_20'))
                .when(pl.col('sma_20') < pl.col('sma_50'))
                .then(pl.lit('bear'))
                .otherwise(pl.lit('sideways'))
                .alias('market_phase')
            ])
            
            # Momentum Indicators
            df = await self._add_momentum_indicators(df)
            
            # Volume Profile Analysis
            df = df.with_columns([
                (pl.col('close') * pl.col('volume')).alias('dollar_volume'),
                pl.col('volume').rolling_mean(window_size=20).alias('avg_volume_20')
            ])
            
            # VWAP calculation
            df = df.with_columns([
                ((pl.col('high') + pl.col('low') + pl.col('close')) / 3 * pl.col('volume')).cumsum().alias('cum_pv'),
                pl.col('volume').cumsum().alias('cum_volume')
            ])
            
            df = df.with_columns([
                (pl.col('cum_pv') / pl.col('cum_volume')).alias('vwap')
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add market condition features: {e}")
            return df
    
    async def _add_time_based_features(self, df: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Add Time-Based Features"""
        try:
            # Extract time components
            df = df.with_columns([
                pl.col('timestamp').dt.hour().alias('hour'),
                pl.col('timestamp').dt.minute().alias('minute'),
                pl.col('timestamp').dt.weekday().alias('weekday'),
                pl.col('timestamp').dt.day().alias('day'),
                pl.col('timestamp').dt.month().alias('month')
            ])
            
            # Intraday Patterns
            df = df.with_columns([
                # High volatility period (9:15-9:45 AM)
                pl.when((pl.col('hour') == 9) & (pl.col('minute') >= 15) & (pl.col('minute') <= 45))
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .alias('high_vol_period'),
                
                # Closing effects (2:30-3:30 PM)
                pl.when((pl.col('hour') >= 14) & (pl.col('hour') <= 15))
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .alias('closing_period'),
                
                # Weekly expiry effects (Thursday for Bank Nifty)
                pl.when(pl.col('weekday') == 4)  # Thursday
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .alias('weekly_expiry_day')
            ])
            
            # Time Decay Features
            if 'expiry_date' in df.columns:
                df = df.with_columns([
                    # Calculate days to expiry (simplified)
                    pl.lit(7).alias('days_to_expiry'),  # Placeholder - would need actual expiry calculation
                    pl.lit(0.05).alias('theta_decay_rate')  # Placeholder theta
                ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add time-based features: {e}")
            return df
    
    async def _add_option_specific_features(self, df: pl.DataFrame, underlying: str) -> pl.DataFrame:
        """Add Option-Specific Features"""
        try:
            # Put-Call Ratio Analysis
            if 'option_type' in df.columns:
                # Group by timestamp to calculate PCR
                pcr_data = df.group_by('timestamp').agg([
                    pl.col('volume').filter(pl.col('option_type') == 'PE').sum().alias('put_volume'),
                    pl.col('volume').filter(pl.col('option_type') == 'CE').sum().alias('call_volume'),
                    pl.col('open_interest').filter(pl.col('option_type') == 'PE').sum().alias('put_oi'),
                    pl.col('open_interest').filter(pl.col('option_type') == 'CE').sum().alias('call_oi')
                ])
                
                pcr_data = pcr_data.with_columns([
                    (pl.col('put_volume') / pl.col('call_volume')).alias('pcr_volume'),
                    (pl.col('put_oi') / pl.col('call_oi')).alias('pcr_oi')
                ])
                
                # Join back to main dataframe
                df = df.join(pcr_data, on='timestamp', how='left')
            
            # Strike-wise Analysis
            if 'strike_price' in df.columns:
                # Determine moneyness
                current_price = df.select(pl.col('close').mean()).item(0, 0)
                
                df = df.with_columns([
                    (pl.col('strike_price') - current_price).alias('strike_distance'),
                    pl.when(pl.abs(pl.col('strike_price') - current_price) <= 100)
                    .then(pl.lit('ATM'))
                    .when(pl.col('strike_price') > current_price)
                    .then(pl.lit('OTM_CALL'))
                    .otherwise(pl.lit('OTM_PUT'))
                    .alias('moneyness')
                ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add option-specific features: {e}")
            return df
    
    async def _add_greeks_volatility_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add Greeks and Volatility Features"""
        try:
            # Simplified Greeks calculation (would need proper Black-Scholes implementation)
            df = df.with_columns([
                pl.lit(0.5).alias('delta'),  # Placeholder
                pl.lit(0.02).alias('gamma'), # Placeholder
                pl.lit(-0.05).alias('theta'), # Placeholder
                pl.lit(0.15).alias('vega'),  # Placeholder
                pl.lit(0.01).alias('rho')    # Placeholder
            ])
            
            # Volatility Features
            df = df.with_columns([
                # Historical volatility
                (pl.col('close').pct_change().rolling_std(window_size=20) * (252**0.5) * 100).alias('historical_vol_20'),
                (pl.col('close').pct_change().rolling_std(window_size=5) * (252**0.5) * 100).alias('historical_vol_5'),
                
                # Implied volatility (placeholder)
                pl.lit(25.0).alias('implied_vol'),
                
                # Volatility rank and percentile
                pl.lit(50.0).alias('vol_rank'),
                pl.lit(0.5).alias('vol_percentile')
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add Greeks and volatility features: {e}")
            return df
    
    async def _add_microstructure_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add Market Microstructure Features"""
        try:
            # Order Flow Analysis
            df = df.with_columns([
                # Large block trades indicator
                pl.when(pl.col('volume') > pl.col('volume').quantile(0.95))
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .alias('large_block_trade'),
                
                # Unusual options activity
                pl.when(pl.col('volume') > pl.col('volume').rolling_mean(window_size=20) * 2)
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .alias('unusual_activity'),
                
                # Market maker behavior (using VWAP deviation)
                (pl.col('close') - pl.col('vwap')).alias('vwap_deviation')
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add microstructure features: {e}")
            return df
    
    async def _add_strategy_performance_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add Strategy Performance Features"""
        try:
            # Backtesting Metrics (simplified)
            df = df.with_columns([
                # Returns calculation
                pl.col('close').pct_change().alias('returns'),
            ])
            
            # Rolling performance metrics
            df = df.with_columns([
                # Sharpe ratio (simplified)
                (pl.col('returns').rolling_mean(window_size=20) / 
                 pl.col('returns').rolling_std(window_size=20)).alias('rolling_sharpe_20'),
                
                # Maximum drawdown (simplified)
                pl.col('close').rolling_max(window_size=50).alias('rolling_max_50')
            ])
            
            df = df.with_columns([
                ((pl.col('close') - pl.col('rolling_max_50')) / pl.col('rolling_max_50')).alias('drawdown')
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add strategy performance features: {e}")
            return df
    
    async def _add_external_factors_features(self, df: pl.DataFrame, index_data: Optional[pl.DataFrame]) -> pl.DataFrame:
        """Add External Factors Features"""
        try:
            # Global Market Integration (using proxies)
            df = df.with_columns([
                pl.lit(100.0).alias('dollar_index'),  # Placeholder
                pl.lit(0.8).alias('global_equity_correlation'),  # Placeholder
                pl.lit(25000.0).alias('sgx_nifty')  # Placeholder
            ])
            
            # News and Events (using time-based proxies)
            df = df.with_columns([
                # RBI meeting days (placeholder - would need actual calendar)
                pl.when(pl.col('day') == 15)  # Assume 15th of month
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .alias('rbi_meeting_day'),
                
                # Earnings season (placeholder)
                pl.when(pl.col('month').is_in([1, 4, 7, 10]))  # Quarterly months
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .alias('earnings_season')
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add external factors features: {e}")
            return df
    
    async def _add_momentum_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Add momentum indicators (RSI, MACD, Stochastic)"""
        try:
            # RSI calculation
            delta = df.select(pl.col('close').diff().alias('delta'))['delta']
            gain = delta.map_elements(lambda x: x if x > 0 else 0, return_dtype=pl.Float64)
            loss = delta.map_elements(lambda x: -x if x < 0 else 0, return_dtype=pl.Float64)
            
            # Simple RSI approximation
            df = df.with_columns([
                pl.lit(50.0).alias('rsi_14'),  # Placeholder RSI
                pl.lit(0.0).alias('macd'),     # Placeholder MACD
                pl.lit(50.0).alias('stoch_k'), # Placeholder Stochastic %K
                pl.lit(50.0).alias('stoch_d')  # Placeholder Stochastic %D
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add momentum indicators: {e}")
            return df
    
    async def _save_comprehensive_features(self, features: pl.DataFrame, underlying: str, timeframe: str):
        """Save comprehensive features with proper naming"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"feature_{underlying}_{timeframe}_{timestamp}.parquet"
            filepath = self.features_path / timeframe / filename
            
            # Save with brotli compression
            features.write_parquet(filepath, compression="brotli")
            logger.info(f"[SAVE] Saved {features.height} feature records to {filename}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save comprehensive features: {e}")
    
    async def _process_timeframe_features(self, underlying: str, timeframe: str):
        """Process features for specific underlying and timeframe"""
        try:
            logger.info(f"[PROCESS] Processing {timeframe} features for {underlying}...")

            # Load timeframe data
            timeframe_data = await self._load_timeframe_data(underlying, timeframe)

            if timeframe_data is None or timeframe_data.height == 0:
                logger.warning(f"[WARNING] No {timeframe} data found for {underlying}")
                return

            # Load underlying OHLC data
            underlying_ohlc = await self._load_underlying_ohlc_data(underlying, timeframe)

            # Calculate Greeks
            greeks_data = await self._calculate_greeks(timeframe_data)

            # Calculate volatility metrics
            volatility_data = await self._calculate_volatility_metrics(timeframe_data)

            # Calculate technical features (use underlying OHLC if available, otherwise timeframe data)
            data_for_technical = underlying_ohlc if not underlying_ohlc.is_empty() else timeframe_data
            technical_features = await self._calculate_technical_features(data_for_technical, timeframe)

            # Calculate options flow indicators
            flow_indicators = await self._calculate_options_flow(timeframe_data)

            # Calculate market regime detection features
            regime_features = await self._calculate_market_regime_features(data_for_technical, timeframe)

            # Calculate option-specific technical features
            option_tech_features = await self._calculate_option_specific_features(timeframe_data, underlying)

            # Calculate derived quant features
            quant_features = await self._calculate_derived_quant_features(data_for_technical, timeframe)

            # Calculate advanced features (liquidity, timeframe-aware, strategy-specific, etc.)
            advanced_features = await self._calculate_advanced_features(timeframe_data, underlying_ohlc, timeframe)

            # Combine all features
            combined_features = await self._combine_features(
                timeframe_data, greeks_data, volatility_data,
                technical_features, flow_indicators, regime_features,
                option_tech_features, quant_features, advanced_features
            )

            # Create enriched option chain data for strategy generation
            enriched_option_chain = await self._create_enriched_option_chain(
                underlying, timeframe, combined_features, underlying_ohlc
            )

            # Save features for this timeframe
            await self._save_timeframe_features(underlying, timeframe, combined_features)

            # Save enriched option chain data
            if not enriched_option_chain.is_empty():
                await self._save_enriched_option_chain(underlying, timeframe, enriched_option_chain)

            logger.info(f"[SUCCESS] {timeframe} features processed for {underlying}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to process {timeframe} features for {underlying}: {e}")

    async def _load_timeframe_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load data for specific underlying and timeframe"""
        try:
            # Check both historical and live data
            timeframe_path = self.historical_path / timeframe

            # Try different file patterns for combined data files
            patterns = [
                f"{underlying}_{timeframe}_*.parquet",  # Original pattern
                f"historical_{timeframe}_*.parquet",    # New pattern for combined files
                f"historical_{timeframe}.parquet"       # Simple pattern
            ]

            files = []
            for pattern in patterns:
                files = list(timeframe_path.glob(pattern))
                if files:
                    break

            if not files:
                # Try live data if no historical data
                timeframe_path = self.live_path / timeframe
                for pattern in patterns:
                    files = list(timeframe_path.glob(pattern))
                    if files:
                        break

            if not files:
                logger.warning(f"[WARNING] No {timeframe} data files found for {underlying}")
                return None

            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)

            # Set environment variable to handle timezone parsing issues
            import os
            os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

            df = pl.read_parquet(latest_file)

            # Filter for the specific underlying if the file contains multiple symbols
            if 'symbol' in df.columns:
                df = df.filter(pl.col('symbol') == underlying)

            if df.height == 0:
                logger.warning(f"[WARNING] No {timeframe} data found for {underlying}")
                return None

            # Add open_interest column if it doesn't exist (use volume as proxy)
            if 'open_interest' not in df.columns:
                df = df.with_columns([
                    (pl.col('volume') * 1.5).alias('open_interest')  # Simple proxy: volume * 1.5
                ])

            logger.info(f"[LOAD] Loaded {timeframe} data for {underlying}: {df.height} records")
            return df

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} data for {underlying}: {e}")
            return None
    
    async def _load_underlying_ohlc_data(self, underlying: str, timeframe: str) -> pl.DataFrame:
        """Load underlying OHLC data for strategy generation"""
        try:
            # Look for underlying OHLC data in multiple locations
            possible_paths = [
                self.historical_path / timeframe,
                self.live_path / timeframe,
                Path("data/underlying") / timeframe,
                Path("data/historical") / timeframe
            ]

            for path in possible_paths:
                if not path.exists():
                    continue

                # Try different file patterns
                patterns = [
                    f"{underlying}_{timeframe}_*.parquet",
                    f"{underlying}_*.parquet",
                    f"*{underlying}*.parquet",
                    f"historical_{timeframe}_*.parquet",    # New pattern for combined files
                    f"historical_{timeframe}.parquet"       # Simple pattern
                ]

                for pattern in patterns:
                    files = list(path.glob(pattern))
                    if files:
                        # Load the most recent file
                        latest_file = max(files, key=lambda x: x.stat().st_mtime)

                        # Set environment variable to handle timezone parsing issues
                        import os
                        os.environ['POLARS_IGNORE_TIMEZONE_PARSE_ERROR'] = '1'

                        df = pl.read_parquet(latest_file)

                        # Filter for the specific underlying if the file contains multiple symbols
                        if 'symbol' in df.columns:
                            df = df.filter(pl.col('symbol') == underlying)

                        # Ensure required columns exist
                        required_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
                        if all(col in df.columns for col in required_cols) and df.height > 0:
                            logger.info(f"[LOAD] Loaded underlying OHLC data for {underlying}: {df.height} records")
                            return df.with_columns([
                                pl.col('timestamp').alias('datetime'),
                                pl.lit(underlying).alias('symbol'),
                                pl.col('close').shift(1).alias('close_prev')
                            ])

            # If no OHLC data found, create synthetic data from options data
            logger.warning(f"[WARNING] No underlying OHLC data found for {underlying}, creating synthetic data")
            return await self._create_synthetic_ohlc_data(underlying, timeframe)

        except Exception as e:
            logger.error(f"[ERROR] Failed to load underlying OHLC data for {underlying}: {e}")
            return pl.DataFrame()

    async def _create_synthetic_ohlc_data(self, underlying: str, timeframe: str) -> pl.DataFrame:
        """Create synthetic OHLC data from options data"""
        try:
            # Load options data to extract underlying prices
            options_data = await self._load_options_data()
            if options_data.is_empty():
                return pl.DataFrame()

            # Filter for this underlying and extract unique timestamps and prices
            underlying_options = options_data.filter(pl.col('underlying') == underlying)

            if 'underlying_price' in underlying_options.columns:
                # Group by timestamp and create OHLC from underlying_price
                ohlc_data = underlying_options.group_by('timestamp').agg([
                    pl.col('underlying_price').first().alias('open'),
                    pl.col('underlying_price').max().alias('high'),
                    pl.col('underlying_price').min().alias('low'),
                    pl.col('underlying_price').last().alias('close'),
                    pl.lit(1000).alias('volume')  # Synthetic volume
                ]).sort('timestamp')

                # Add required columns
                ohlc_data = ohlc_data.with_columns([
                    pl.col('timestamp').alias('datetime'),
                    pl.lit(underlying).alias('symbol'),
                    pl.col('close').shift(1).alias('close_prev')
                ])

                logger.info(f"[SYNTHETIC] Created synthetic OHLC data for {underlying}: {ohlc_data.height} records")
                return ohlc_data

            return pl.DataFrame()

        except Exception as e:
            logger.error(f"[ERROR] Failed to create synthetic OHLC data for {underlying}: {e}")
            return pl.DataFrame()

    async def _load_historical_options_data(self, underlying: str) -> Optional[pl.DataFrame]:
        """Load historical options data"""
        try:
            # Find the latest historical data file
            pattern = f"{underlying}_historical_*.parquet"
            files = list(self.historical_path.glob(pattern))

            if not files:
                logger.warning(f"[WARNING] No historical data files found for {underlying}")
                return None
            
            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            df = pl.read_parquet(latest_file)
            
            logger.info(f"[LOAD] Loaded {df.height} records for {underlying}")
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load historical data for {underlying}: {e}")
            return None
    
    def _calculate_rsi_manual(self, close_col: pl.Expr, period: int) -> pl.Expr:
        """Manual RSI calculation using polars"""
        delta = close_col.diff()
        gain = delta.clip(lower_bound=0)
        loss = (-delta).clip(lower_bound=0)

        avg_gain = gain.rolling_mean(window_size=period)
        avg_loss = loss.rolling_mean(window_size=period)

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fill_null(50.0)  # Default RSI value

    def _calculate_ema_manual(self, close_col: pl.Expr, period: int) -> pl.Expr:
        """Manual EMA calculation using polars"""
        alpha = 2.0 / (period + 1)
        return close_col.ewm_mean(alpha=alpha)

    def _calculate_atr_manual(self, high_col: pl.Expr, low_col: pl.Expr, close_col: pl.Expr, period: int) -> pl.Expr:
        """Manual ATR calculation using polars"""
        tr1 = high_col - low_col
        tr2 = (high_col - close_col.shift(1)).abs()
        tr3 = (low_col - close_col.shift(1)).abs()

        tr = pl.max_horizontal([tr1, tr2, tr3])
        atr = tr.ewm_mean(span=period)
        return atr

    def _calculate_supertrend_manual(self, data: pl.DataFrame, period: int = 10, multiplier: float = 3.0) -> pl.Expr:
        """Manual SuperTrend calculation using polars"""
        try:
            if not all(col in data.columns for col in ['high', 'low', 'close']):
                # Use close price as fallback
                return pl.col('close')

            # Calculate ATR
            atr = self._calculate_atr_manual(pl.col('high'), pl.col('low'), pl.col('close'), period)

            # Calculate HLA (High Low Average)
            hla = (pl.col('high') + pl.col('low')) / 2

            # Calculate basic bands
            basic_upper = hla + (multiplier * atr)
            basic_lower = hla - (multiplier * atr)

            # For simplicity, return basic upper band as SuperTrend
            # In a full implementation, you'd need to implement the conditional logic
            return basic_upper.fill_null(pl.col('close'))

        except Exception as e:
            # Fallback to close price
            return pl.col('close')

    def _extract_expiry_from_symbol(self, symbol: str) -> str:
        """Extract expiry date from option symbol"""
        try:
            # Pattern: NIFTY24JUL25 or BANKNIFTY31JUL25
            import re

            # Extract date pattern (e.g., 24JUL25, 31JUL25)
            pattern = r'(\d{1,2}[A-Z]{3}\d{2})'
            match = re.search(pattern, symbol)

            if match:
                date_str = match.group(1)
                # Convert to standard format
                # 24JUL25 -> 2025-07-24
                # 31JUL25 -> 2025-07-31

                day = date_str[:2] if len(date_str) >= 7 else date_str[:1]
                month_str = date_str[-5:-2]
                year = "20" + date_str[-2:]

                month_map = {
                    'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
                    'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
                    'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
                }

                month = month_map.get(month_str, '07')
                return f"{year}-{month}-{day.zfill(2)}"

            # Default to a future date if parsing fails
            return "2025-07-31"

        except Exception as e:
            logger.warning(f"[WARNING] Failed to extract expiry from {symbol}: {e}")
            return "2025-07-31"

    async def _calculate_greeks(self, data: pl.DataFrame) -> pl.DataFrame:
        """Calculate options Greeks"""
        try:
            logger.info("[GREEKS] Calculating options Greeks...")

            # Filter for options data only (exclude underlying)
            options_data = data.filter(
                (pl.col('option_type').is_not_null()) &
                (pl.col('strike_price').is_not_null())
            )

            if options_data.height == 0:
                logger.warning("[WARNING] No options data found for Greeks calculation")
                return pl.DataFrame()

            # Add expiry_date column by extracting from symbol
            options_data = options_data.with_columns([
                pl.col('symbol').map_elements(
                    lambda x: self._extract_expiry_from_symbol(x),
                    return_dtype=pl.Utf8
                ).alias('expiry_date')
            ])

            # Prepare data for Greeks calculation
            greeks_data = []

            for row in options_data.iter_rows(named=True):
                try:
                    # Extract parameters
                    spot_price = row.get('underlying_price', 25000)  # Default spot price
                    strike_price = row['strike_price']
                    time_to_expiry = self._calculate_time_to_expiry(row['expiry_date'])
                    option_type = row['option_type']
                    option_price = row['close']
                    
                    # Calculate implied volatility
                    try:
                        if option_type == 'CE':
                            iv = implied_volatility(option_price, spot_price, strike_price, 
                                                  time_to_expiry, self.risk_free_rate, 'c')
                        else:
                            iv = implied_volatility(option_price, spot_price, strike_price, 
                                                  time_to_expiry, self.risk_free_rate, 'p')
                    except:
                        iv = 0.20  # Default IV
                    
                    # Calculate Greeks
                    if option_type == 'CE':
                        delta = greeks.delta('c', spot_price, strike_price, time_to_expiry, 
                                           self.risk_free_rate, iv)
                        gamma = greeks.gamma('c', spot_price, strike_price, time_to_expiry, 
                                           self.risk_free_rate, iv)
                        theta = greeks.theta('c', spot_price, strike_price, time_to_expiry, 
                                           self.risk_free_rate, iv)
                        vega = greeks.vega('c', spot_price, strike_price, time_to_expiry, 
                                         self.risk_free_rate, iv)
                        rho = greeks.rho('c', spot_price, strike_price, time_to_expiry, 
                                       self.risk_free_rate, iv)
                    else:
                        delta = greeks.delta('p', spot_price, strike_price, time_to_expiry, 
                                           self.risk_free_rate, iv)
                        gamma = greeks.gamma('p', spot_price, strike_price, time_to_expiry, 
                                           self.risk_free_rate, iv)
                        theta = greeks.theta('p', spot_price, strike_price, time_to_expiry, 
                                           self.risk_free_rate, iv)
                        vega = greeks.vega('p', spot_price, strike_price, time_to_expiry, 
                                         self.risk_free_rate, iv)
                        rho = greeks.rho('p', spot_price, strike_price, time_to_expiry, 
                                       self.risk_free_rate, iv)
                    
                    greeks_data.append({
                        'symbol': row['symbol'],
                        'timestamp': row['timestamp'],
                        'delta': delta,
                        'gamma': gamma,
                        'theta': theta,
                        'vega': vega,
                        'rho': rho,
                        'implied_vol': iv,
                        'time_to_expiry': time_to_expiry
                    })
                    
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to calculate Greeks for {row['symbol']}: {e}")
                    continue
            
            greeks_df = pl.DataFrame(greeks_data)
            logger.info(f"[SUCCESS] Calculated Greeks for {len(greeks_data)} options")
            
            return greeks_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate Greeks: {e}")
            return pl.DataFrame()
    
    def _calculate_time_to_expiry(self, expiry_date: str) -> float:
        """Calculate time to expiry in years"""
        try:
            if isinstance(expiry_date, str):
                expiry = datetime.strptime(expiry_date, '%Y-%m-%d')
            else:
                expiry = expiry_date
            
            now = datetime.now()
            days_to_expiry = (expiry - now).days
            return max(days_to_expiry / 365.0, 0.001)  # Minimum 1 day
            
        except Exception as e:
            logger.warning(f"[WARNING] Failed to calculate time to expiry: {e}")
            return 0.1  # Default to ~36 days
    
    async def _calculate_volatility_metrics(self, data: pl.DataFrame) -> pl.DataFrame:
        """Calculate volatility metrics using polars only"""
        try:
            logger.info("[VOLATILITY] Calculating volatility metrics...")

            # Group by symbol for volatility calculations
            volatility_data = []

            symbols = data['symbol'].unique().to_list()

            for symbol in symbols:
                symbol_data = data.filter(pl.col('symbol') == symbol).sort('timestamp')

                if symbol_data.height < self.config['volatility_window']:
                    continue

                # Calculate log returns using polars
                symbol_data = symbol_data.with_columns([
                    (pl.col('close').log() - pl.col('close').log().shift(1)).alias('log_returns')
                ]).drop_nulls()

                if symbol_data.height < 2:
                    continue

                # Calculate historical volatility using polars
                returns_std = symbol_data['log_returns'].std()
                hist_vol = returns_std * math.sqrt(252) if returns_std is not None else 0.0

                # Calculate rolling volatility using polars
                window = self.config['volatility_window']
                if symbol_data.height >= window:
                    rolling_vol_data = symbol_data.with_columns([
                        pl.col('log_returns').rolling_std(window_size=window).alias('rolling_vol')
                    ]).drop_nulls()

                    if rolling_vol_data.height > 0:
                        # Annualize rolling volatility
                        rolling_vol_data = rolling_vol_data.with_columns([
                            (pl.col('rolling_vol') * math.sqrt(252)).alias('rolling_vol_annualized')
                        ])

                        current_vol = rolling_vol_data['rolling_vol_annualized'].tail(1).item()
                        vol_mean = rolling_vol_data['rolling_vol_annualized'].mean()
                        vol_std = rolling_vol_data['rolling_vol_annualized'].std()
                        vol_percentile = rolling_vol_data['rolling_vol_annualized'].quantile(0.5)

                        # Calculate volatility rank (percentile of current vol in historical range)
                        vol_values = rolling_vol_data['rolling_vol_annualized'].to_list()
                        vol_rank = sum(1 for v in vol_values if v <= current_vol) / len(vol_values)
                    else:
                        current_vol = hist_vol
                        vol_mean = hist_vol
                        vol_std = 0.0
                        vol_percentile = hist_vol
                        vol_rank = 0.5
                else:
                    current_vol = hist_vol
                    vol_mean = hist_vol
                    vol_std = 0.0
                    vol_percentile = hist_vol
                    vol_rank = 0.5

                volatility_data.append({
                    'symbol': symbol,
                    'timestamp': symbol_data['timestamp'].max(),
                    'historical_vol': hist_vol,
                    'current_vol': current_vol,
                    'vol_rank': vol_rank,
                    'vol_percentile': vol_percentile,
                    'vol_mean': vol_mean,
                    'vol_std': vol_std
                })

            volatility_df = pl.DataFrame(volatility_data)
            logger.info(f"[SUCCESS] Calculated volatility metrics for {len(volatility_data)} symbols")

            return volatility_df

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate volatility metrics: {e}")
            return pl.DataFrame()
    
    async def _calculate_technical_features(self, data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate timeframe-specific technical analysis features using polars-talib"""
        try:
            logger.info(f"[TECHNICAL] Calculating {timeframe} technical features...")

            # Group by underlying for technical analysis
            technical_results = []

            # Check if we have 'underlying' or 'symbol' column
            if 'underlying' in data.columns:
                underlyings = data['underlying'].unique().to_list()
                group_col = 'underlying'
            elif 'symbol' in data.columns:
                # Filter for underlying symbols only (not options)
                underlyings = data.filter(pl.col('option_type').is_null())['symbol'].unique().to_list()
                group_col = 'symbol'
            else:
                logger.warning("[WARNING] No underlying or symbol column found")
                return pl.DataFrame()

            for underlying in underlyings:
                underlying_data = data.filter(pl.col(group_col) == underlying).sort('timestamp')

                if underlying_data.height < 20:  # Need minimum data for indicators
                    continue

                # Adjust periods based on timeframe
                period_multiplier = self._get_period_multiplier(timeframe)

                # Calculate technical indicators with fallback implementation
                try:
                    # Try polars-talib first, fallback to manual calculation
                    try:
                        # RSI
                        rsi_period = max(1, 14 // period_multiplier)
                        underlying_data = underlying_data.with_columns([
                            pl.col('close').ta.rsi(timeperiod=rsi_period).alias('rsi_14')
                        ])
                    except:
                        # Manual RSI calculation
                        rsi_period = max(1, 14 // period_multiplier)
                        underlying_data = underlying_data.with_columns([
                            self._calculate_rsi_manual(pl.col('close'), rsi_period).alias('rsi_14')
                        ])

                    # Simple Moving Average
                    sma_period = max(1, 20 // period_multiplier)
                    underlying_data = underlying_data.with_columns([
                        pl.col('close').rolling_mean(window_size=sma_period).alias('sma_20')
                    ])

                    # Multiple Exponential Moving Averages (using manual EMA)
                    underlying_data = underlying_data.with_columns([
                        self._calculate_ema_manual(pl.col('close'), max(1, 5 // period_multiplier)).alias('ema_5'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 10 // period_multiplier)).alias('ema_10'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 12 // period_multiplier)).alias('ema_12'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 13 // period_multiplier)).alias('ema_13'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 20 // period_multiplier)).alias('ema_20'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 21 // period_multiplier)).alias('ema_21'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 30 // period_multiplier)).alias('ema_30'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 50 // period_multiplier)).alias('ema_50'),
                        self._calculate_ema_manual(pl.col('close'), max(1, 100 // period_multiplier)).alias('ema_100')
                    ])

                    # Bollinger Bands (manual calculation)
                    bb_period = max(1, 20 // period_multiplier)
                    underlying_data = underlying_data.with_columns([
                        pl.col('close').rolling_mean(window_size=bb_period).alias('bb_middle'),
                        pl.col('close').rolling_std(window_size=bb_period).alias('bb_std')
                    ]).with_columns([
                        (pl.col('bb_middle') + 2 * pl.col('bb_std')).alias('bb_upper'),
                        (pl.col('bb_middle') - 2 * pl.col('bb_std')).alias('bb_lower')
                    ])

                    # Average True Range (manual calculation)
                    if all(col in underlying_data.columns for col in ['high', 'low']):
                        atr_period = max(1, 14 // period_multiplier)
                        underlying_data = underlying_data.with_columns([
                            self._calculate_atr_manual(pl.col('high'), pl.col('low'), pl.col('close'), atr_period).alias('atr')
                        ])
                    else:
                        underlying_data = underlying_data.with_columns([pl.lit(0.0).alias('atr')])

                    # Average Directional Index (ADX) (simplified using price momentum)
                    if all(col in underlying_data.columns for col in ['high', 'low']):
                        adx_period = max(1, 14 // period_multiplier)
                        underlying_data = underlying_data.with_columns([
                            ((pl.col('close') - pl.col('close').shift(adx_period)).abs() /
                             pl.col('close').shift(adx_period) * 100).rolling_mean(window_size=adx_period).alias('adx')
                        ])
                    else:
                        underlying_data = underlying_data.with_columns([pl.lit(50.0).alias('adx')])

                    # VWAP (Volume Weighted Average Price) - fixed cumsum issue
                    underlying_data = underlying_data.with_columns([
                        ((pl.col('high').fill_null(pl.col('close')) + pl.col('low').fill_null(pl.col('close')) + pl.col('close')) / 3).alias('typical_price')
                    ]).with_columns([
                        (pl.col('typical_price') * pl.col('volume')).alias('price_volume')
                    ]).with_columns([
                        (pl.col('price_volume').sum() / pl.col('volume').sum()).alias('vwap')
                    ])

                    # RSI with different periods
                    underlying_data = underlying_data.with_columns([
                        pl.col('close').ta.rsi(timeperiod=max(1, 5 // period_multiplier)).alias('rsi_5')
                    ])

                    # Volume SMA
                    vol_sma_period = max(1, 20 // period_multiplier)
                    underlying_data = underlying_data.with_columns([
                        pl.col('volume').ta.sma(timeperiod=vol_sma_period).alias('volume_sma')
                    ])

                    # Price change and volume ratio using polars
                    underlying_data = underlying_data.with_columns([
                        ((pl.col('close') - pl.col('close').shift(1)) / pl.col('close').shift(1)).alias('price_change'),
                        (pl.col('volume') / pl.col('volume_sma')).alias('volume_ratio')
                    ])

                    # Additional technical indicators (manual MACD)
                    underlying_data = underlying_data.with_columns([
                        (self._calculate_ema_manual(pl.col('close'), 12) - self._calculate_ema_manual(pl.col('close'), 26)).alias('macd')
                    ]).with_columns([
                        self._calculate_ema_manual(pl.col('macd'), 9).alias('macd_signal')
                    ]).with_columns([
                        (pl.col('macd') - pl.col('macd_signal')).alias('macd_hist')
                    ])

                    # Stochastic oscillator (manual calculation)
                    if all(col in underlying_data.columns for col in ['high', 'low']):
                        underlying_data = underlying_data.with_columns([
                            pl.col('high').rolling_max(window_size=14).alias('highest_high_stoch'),
                            pl.col('low').rolling_min(window_size=14).alias('lowest_low_stoch')
                        ]).with_columns([
                            (((pl.col('close') - pl.col('lowest_low_stoch')) /
                              (pl.col('highest_high_stoch') - pl.col('lowest_low_stoch'))) * 100).alias('stoch_k_raw')
                        ]).with_columns([
                            pl.col('stoch_k_raw').rolling_mean(window_size=3).alias('stoch_k'),
                            pl.col('stoch_k_raw').rolling_mean(window_size=3).rolling_mean(window_size=3).alias('stoch_d')
                        ])
                    else:
                        underlying_data = underlying_data.with_columns([
                            pl.lit(50.0).alias('stoch_k'),
                            pl.lit(50.0).alias('stoch_d')
                        ])

                    # Williams %R (manual calculation)
                    if all(col in underlying_data.columns for col in ['high', 'low']):
                        underlying_data = underlying_data.with_columns([
                            pl.col('high').rolling_max(window_size=14).alias('highest_high_14'),
                            pl.col('low').rolling_min(window_size=14).alias('lowest_low_14')
                        ]).with_columns([
                            (((pl.col('highest_high_14') - pl.col('close')) /
                              (pl.col('highest_high_14') - pl.col('lowest_low_14'))) * -100).alias('williams_r')
                        ])
                    else:
                        underlying_data = underlying_data.with_columns([pl.lit(-50.0).alias('williams_r')])

                    # Commodity Channel Index (simplified calculation)
                    if all(col in underlying_data.columns for col in ['high', 'low']):
                        underlying_data = underlying_data.with_columns([
                            ((pl.col('high') + pl.col('low') + pl.col('close')) / 3).alias('typical_price')
                        ]).with_columns([
                            pl.col('typical_price').rolling_mean(window_size=14).alias('tp_sma'),
                            pl.col('typical_price').rolling_std(window_size=14).alias('tp_std')
                        ]).with_columns([
                            ((pl.col('typical_price') - pl.col('tp_sma')) / (0.015 * pl.col('tp_std'))).alias('cci')
                        ])
                    else:
                        underlying_data = underlying_data.with_columns([pl.lit(0.0).alias('cci')])

                    # Money Flow Index (simplified using volume ratio)
                    if all(col in underlying_data.columns for col in ['high', 'low', 'volume']):
                        underlying_data = underlying_data.with_columns([
                            (pl.col('volume') / pl.col('volume').rolling_mean(window_size=14) * 50).alias('mfi')
                        ])
                    else:
                        underlying_data = underlying_data.with_columns([pl.lit(50.0).alias('mfi')])

                    # Donchian Channels (check if high/low columns exist)
                    donchian_period = max(1, 20 // period_multiplier)
                    if all(col in underlying_data.columns for col in ['high', 'low']):
                        underlying_data = underlying_data.with_columns([
                            pl.col('high').rolling_max(window_size=donchian_period).alias('donchian_high'),
                            pl.col('low').rolling_min(window_size=donchian_period).alias('donchian_low')
                        ])
                    else:
                        underlying_data = underlying_data.with_columns([
                            pl.col('close').rolling_max(window_size=donchian_period).alias('donchian_high'),
                            pl.col('close').rolling_min(window_size=donchian_period).alias('donchian_low')
                        ])

                    # SuperTrend Indicator (manual calculation)
                    try:
                        supertrend_data = self._calculate_supertrend_manual(underlying_data, period=10, multiplier=3.0)
                        underlying_data = underlying_data.with_columns([
                            supertrend_data.alias('supertrend')
                        ])
                    except Exception as e:
                        logger.warning(f"[WARNING] Failed to calculate SuperTrend: {e}")
                        underlying_data = underlying_data.with_columns([
                            pl.col('close').alias('supertrend')  # Use close price as fallback
                        ])

                    # Pivot Points and Support/Resistance
                    pivot_data = self._calculate_pivot_points(underlying_data)
                    underlying_data = underlying_data.with_columns([
                        pivot_data['pivot'].alias('pivot'),
                        pivot_data['support'].alias('support'),
                        pivot_data['resistance'].alias('resistance')
                    ])

                    # Central Pivot Range (CPR)
                    cpr_data = self._calculate_cpr(underlying_data)
                    underlying_data = underlying_data.with_columns([
                        cpr_data['cpr_top'].alias('cpr_top'),
                        cpr_data['cpr_bottom'].alias('cpr_bottom')
                    ])

                    # Add underlying column if it doesn't exist
                    if 'underlying' not in underlying_data.columns:
                        underlying_data = underlying_data.with_columns([
                            pl.lit(underlying).alias('underlying')
                        ])

                    # Select relevant columns for output
                    technical_features = underlying_data.select([
                        'underlying', 'timestamp',
                        pl.lit(timeframe).alias('timeframe'),
                        # Basic OHLCV
                        'open', 'high', 'low', 'close', 'volume',
                        # RSI variants
                        'rsi_5', 'rsi_14',
                        # Moving averages
                        'sma_20', 'ema_5', 'ema_10', 'ema_12', 'ema_13', 'ema_20', 'ema_21',
                        'ema_30', 'ema_50', 'ema_100',
                        # Bollinger Bands
                        'bb_upper', 'bb_lower', 'bb_middle',
                        # Momentum indicators
                        'atr', 'adx', 'volume_sma', 'price_change', 'volume_ratio',
                        'macd', 'macd_signal', 'macd_hist',
                        'stoch_k', 'stoch_d', 'williams_r', 'cci', 'mfi',
                        # Advanced indicators
                        'vwap', 'supertrend', 'donchian_high', 'donchian_low',
                        'pivot', 'support', 'resistance', 'cpr_top', 'cpr_bottom'
                    ]).drop_nulls()

                    technical_results.append(technical_features)

                except Exception as e:
                    logger.warning(f"[WARNING] Failed to calculate technical features for {underlying}: {e}")
                    continue

            # Combine all results
            if technical_results:
                technical_df = pl.concat(technical_results)
                logger.info(f"[SUCCESS] Calculated {timeframe} technical features for {technical_df.height} records")
                return technical_df
            else:
                logger.warning(f"[WARNING] No technical features calculated for {timeframe}")
                return pl.DataFrame()

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate {timeframe} technical features: {e}")
            return pl.DataFrame()

    def _get_period_multiplier(self, timeframe: str) -> int:
        """Get period multiplier based on timeframe"""
        multipliers = {
            "1min": 1,
            "3min": 3,
            "5min": 5,
            "15min": 15
        }
        return multipliers.get(timeframe, 1)

    def _calculate_supertrend(self, data: pl.DataFrame, period: int = 10, multiplier: float = 3.0) -> pl.Series:
        """Calculate SuperTrend indicator using polars"""
        try:
            # Calculate ATR
            atr = ta.atr(data['high'], data['low'], data['close'], timeperiod=period)

            # Calculate basic upper and lower bands
            hl2 = (data['high'] + data['low']) / 2
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)

            # Initialize SuperTrend
            supertrend = pl.Series([0.0] * data.height)
            trend = pl.Series([1] * data.height)  # 1 for uptrend, -1 for downtrend

            for i in range(1, data.height):
                # Update bands
                if upper_band[i] < upper_band[i-1] or data['close'][i-1] > upper_band[i-1]:
                    upper_band[i] = upper_band[i]
                else:
                    upper_band[i] = upper_band[i-1]

                if lower_band[i] > lower_band[i-1] or data['close'][i-1] < lower_band[i-1]:
                    lower_band[i] = lower_band[i]
                else:
                    lower_band[i] = lower_band[i-1]

                # Determine trend
                if data['close'][i] <= lower_band[i]:
                    trend[i] = -1
                elif data['close'][i] >= upper_band[i]:
                    trend[i] = 1
                else:
                    trend[i] = trend[i-1]

                # Set SuperTrend value
                if trend[i] == 1:
                    supertrend[i] = lower_band[i]
                else:
                    supertrend[i] = upper_band[i]

            return supertrend

        except Exception as e:
            logger.warning(f"[WARNING] Failed to calculate SuperTrend: {e}")
            return pl.Series([0.0] * data.height)

    def _calculate_pivot_points(self, data: pl.DataFrame) -> Dict[str, pl.Series]:
        """Calculate Pivot Points, Support, and Resistance"""
        try:
            # Calculate pivot point (PP = (H + L + C) / 3)
            pivot = (data['high'] + data['low'] + data['close']) / 3

            # Calculate support and resistance levels
            # R1 = 2*PP - L, S1 = 2*PP - H
            resistance = 2 * pivot - data['low']
            support = 2 * pivot - data['high']

            return {
                'pivot': pivot,
                'support': support,
                'resistance': resistance
            }

        except Exception as e:
            logger.warning(f"[WARNING] Failed to calculate pivot points: {e}")
            return {
                'pivot': pl.Series([0.0] * data.height),
                'support': pl.Series([0.0] * data.height),
                'resistance': pl.Series([0.0] * data.height)
            }

    def _calculate_cpr(self, data: pl.DataFrame) -> Dict[str, pl.Series]:
        """Calculate Central Pivot Range (CPR)"""
        try:
            # CPR calculation for Indian markets
            # Pivot = (H + L + C) / 3
            # TC (Top Central) = (Pivot - BC) + Pivot
            # BC (Bottom Central) = (H + L) / 2

            pivot = (data['high'] + data['low'] + data['close']) / 3
            bc = (data['high'] + data['low']) / 2
            tc = (pivot - bc) + pivot

            return {
                'cpr_top': tc,
                'cpr_bottom': bc
            }

        except Exception as e:
            logger.warning(f"[WARNING] Failed to calculate CPR: {e}")
            return {
                'cpr_top': pl.Series([0.0] * data.height),
                'cpr_bottom': pl.Series([0.0] * data.height)
            }

    async def _create_enriched_option_chain(self, underlying: str, timeframe: str,
                                          features: pl.DataFrame, ohlc_data: pl.DataFrame) -> pl.DataFrame:
        """Create enriched option chain data combining options data with technical features"""
        try:
            # Load current option chain data
            option_chain_data = await self._load_current_option_chain(underlying)

            if option_chain_data.is_empty():
                logger.warning(f"[WARNING] No option chain data found for {underlying}")
                return pl.DataFrame()

            # Get latest technical features
            if not features.is_empty():
                latest_features = features.sort('timestamp').tail(1)

                # Add technical indicators to option chain
                for row in latest_features.iter_rows(named=True):
                    # Add technical features as columns to option chain
                    option_chain_data = option_chain_data.with_columns([
                        pl.lit(row.get('rsi_14', 50)).alias('rsi_14'),
                        pl.lit(row.get('ema_20', 0)).alias('ema_20'),
                        pl.lit(row.get('macd', 0)).alias('macd'),
                        pl.lit(row.get('atr', 0)).alias('atr'),
                        pl.lit(row.get('vwap', 0)).alias('vwap'),
                        pl.lit(row.get('supertrend', 0)).alias('supertrend'),
                        pl.lit(row.get('adx', 0)).alias('adx'),
                        pl.lit(row.get('bb_upper', 0)).alias('bb_upper'),
                        pl.lit(row.get('bb_lower', 0)).alias('bb_lower'),
                        pl.lit(row.get('pivot', 0)).alias('pivot'),
                        pl.lit(row.get('support', 0)).alias('support'),
                        pl.lit(row.get('resistance', 0)).alias('resistance'),
                        pl.lit(row.get('cpr_top', 0)).alias('cpr_top'),
                        pl.lit(row.get('cpr_bottom', 0)).alias('cpr_bottom')
                    ])

            # Add current underlying price from OHLC data
            if not ohlc_data.is_empty():
                latest_ohlc = ohlc_data.sort('timestamp').tail(1)
                current_price = latest_ohlc['close'].item() if latest_ohlc.height > 0 else 0

                option_chain_data = option_chain_data.with_columns([
                    pl.lit(current_price).alias('underlying_price'),
                    pl.lit(current_price).alias('spot_price')
                ])

                # Calculate moneyness for each option
                option_chain_data = option_chain_data.with_columns([
                    (pl.col('strike_price') / current_price).alias('moneyness'),
                    (pl.col('strike_price') - current_price).alias('strike_distance'),
                    pl.when(pl.col('option_type') == 'CE')
                    .then(pl.when(pl.col('strike_price') <= current_price * 1.02).then('ATM')
                         .when(pl.col('strike_price') <= current_price * 1.05).then('OTM')
                         .otherwise('FAR_OTM'))
                    .otherwise(pl.when(pl.col('strike_price') >= current_price * 0.98).then('ATM')
                              .when(pl.col('strike_price') >= current_price * 0.95).then('OTM')
                              .otherwise('FAR_OTM')).alias('moneyness_category')
                ])

            # Add timestamp for when this enriched data was created
            option_chain_data = option_chain_data.with_columns([
                pl.lit(datetime.now()).alias('enriched_timestamp'),
                pl.lit(timeframe).alias('timeframe')
            ])

            logger.info(f"[SUCCESS] Created enriched option chain for {underlying}: {option_chain_data.height} options")
            return option_chain_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to create enriched option chain for {underlying}: {e}")
            return pl.DataFrame()

    async def _load_current_option_chain(self, underlying: str) -> pl.DataFrame:
        """Load current option chain data"""
        try:
            # Look for option chain data in multiple locations
            possible_paths = [
                Path("data/option_chains"),
                Path("data/live/option_chains"),
                Path("data/historical/option_chains")
            ]

            for path in possible_paths:
                if not path.exists():
                    continue

                # Try different file patterns
                patterns = [
                    f"{underlying}_chain_*.parquet",
                    f"{underlying}_options_*.parquet",
                    f"*{underlying}*.parquet"
                ]

                for pattern in patterns:
                    files = list(path.glob(pattern))
                    if files:
                        # Load the most recent file
                        latest_file = max(files, key=lambda x: x.stat().st_mtime)
                        df = pl.read_parquet(latest_file)

                        # Ensure required columns exist
                        required_cols = ['symbol', 'option_type', 'strike_price', 'ltp']
                        if all(col in df.columns for col in required_cols):
                            return df

            logger.warning(f"[WARNING] No option chain data found for {underlying}")
            return pl.DataFrame()

        except Exception as e:
            logger.error(f"[ERROR] Failed to load option chain data for {underlying}: {e}")
            return pl.DataFrame()

    async def _save_enriched_option_chain(self, underlying: str, timeframe: str, data: pl.DataFrame):
        """Save enriched option chain data"""
        try:
            # Create enriched data directory
            enriched_path = self.features_path / "enriched_chains" / timeframe
            enriched_path.mkdir(parents=True, exist_ok=True)

            # Save with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{underlying}_{timeframe}_enriched_chain_{timestamp}.parquet"
            filepath = enriched_path / filename

            data.write_parquet(filepath, compression="snappy")
            logger.info(f"[SAVE] Saved enriched option chain: {filepath}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save enriched option chain: {e}")

    async def _calculate_market_regime_features(self, data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate market regime detection features"""
        try:
            logger.info(f"[REGIME] Calculating market regime features for {timeframe}...")

            if data.height < 50:  # Need sufficient data for regime analysis
                return pl.DataFrame()

            # 1. Trend Indicators
            # Multiple timeframe SMAs and EMAs for trend detection
            data = data.with_columns([
                ta.sma(pl.col('close'), timeperiod=10).alias('sma_10'),
                ta.sma(pl.col('close'), timeperiod=50).alias('sma_50'),
                ta.sma(pl.col('close'), timeperiod=200).alias('sma_200'),
                ta.ema(pl.col('close'), timeperiod=9).alias('ema_9'),
                ta.ema(pl.col('close'), timeperiod=21).alias('ema_21'),
                ta.ema(pl.col('close'), timeperiod=55).alias('ema_55')
            ])

            # MACD slope calculation
            macd_data = ta.macd(data['close'])
            data = data.with_columns([
                macd_data.select('macd').to_series().alias('macd_line'),
                macd_data.select('macdsignal').to_series().alias('macd_signal_line'),
                (macd_data.select('macd').to_series() - macd_data.select('macd').to_series().shift(1)).alias('macd_slope')
            ])

            # Trend strength indicators
            data = data.with_columns([
                # Bull/Bear trend strength
                pl.when(pl.col('close') > pl.col('sma_50')).then(1).otherwise(0).alias('bull_trend'),
                pl.when(pl.col('close') < pl.col('sma_50')).then(1).otherwise(0).alias('bear_trend'),
                # Multi-timeframe trend alignment
                pl.when((pl.col('ema_9') > pl.col('ema_21')) & (pl.col('ema_21') > pl.col('ema_55')))
                .then(1).otherwise(0).alias('trend_alignment_bull'),
                pl.when((pl.col('ema_9') < pl.col('ema_21')) & (pl.col('ema_21') < pl.col('ema_55')))
                .then(1).otherwise(0).alias('trend_alignment_bear')
            ])

            # 2. Volatility Regime Detection
            # Historical volatility bands
            data = data.with_columns([
                (pl.col('close').log() - pl.col('close').log().shift(1)).alias('returns')
            ])

            # Rolling volatility calculations
            vol_window = 20
            data = data.with_columns([
                pl.col('returns').rolling_std(window_size=vol_window).alias('rolling_vol'),
                pl.col('returns').rolling_std(window_size=vol_window * 2).alias('rolling_vol_long')
            ])

            # Volatility regime classification
            data = data.with_columns([
                (pl.col('rolling_vol') * math.sqrt(252)).alias('annualized_vol'),
                pl.col('rolling_vol').rolling_quantile(0.8, window_size=100).alias('vol_80th_percentile'),
                pl.col('rolling_vol').rolling_quantile(0.2, window_size=100).alias('vol_20th_percentile')
            ])

            data = data.with_columns([
                pl.when(pl.col('rolling_vol') > pl.col('vol_80th_percentile')).then('HIGH_VOL')
                .when(pl.col('rolling_vol') < pl.col('vol_20th_percentile')).then('LOW_VOL')
                .otherwise('NORMAL_VOL').alias('volatility_regime'),
                (pl.col('rolling_vol') / pl.col('rolling_vol_long')).alias('vol_ratio')
            ])

            # 3. Momentum Shifts
            # RSI divergence detection (with manual calculation)
            try:
                data = data.with_columns([
                    self._calculate_rsi_manual(pl.col('close'), 14).alias('rsi_14_regime'),
                    pl.col('close').rolling_max(window_size=14).alias('price_high_14'),
                    pl.col('close').rolling_min(window_size=14).alias('price_low_14')
                ])
            except Exception as e:
                logger.warning(f"[WARNING] Failed to calculate RSI: {e}")
                # Add default values
                data = data.with_columns([
                    pl.lit(50.0).alias('rsi_14_regime'),
                    pl.col('close').rolling_max(window_size=14).alias('price_high_14'),
                    pl.col('close').rolling_min(window_size=14).alias('price_low_14')
                ])

            # Stochastic crossovers (with manual calculation)
            try:
                if all(col in data.columns for col in ['high', 'low', 'close']):
                    # Manual Stochastic calculation
                    data = data.with_columns([
                        pl.col('high').rolling_max(window_size=14).alias('highest_high'),
                        pl.col('low').rolling_min(window_size=14).alias('lowest_low')
                    ]).with_columns([
                        (((pl.col('close') - pl.col('lowest_low')) / (pl.col('highest_high') - pl.col('lowest_low'))) * 100).alias('stoch_k_raw')
                    ]).with_columns([
                        pl.col('stoch_k_raw').rolling_mean(window_size=3).alias('stoch_k_regime'),
                        pl.col('stoch_k_raw').rolling_mean(window_size=3).rolling_mean(window_size=3).alias('stoch_d_regime')
                    ])

                    # Calculate crossovers
                    data = data.with_columns([
                        pl.when((pl.col('stoch_k_regime') > pl.col('stoch_d_regime')) &
                               (pl.col('stoch_k_regime').shift(1) <= pl.col('stoch_d_regime').shift(1)))
                        .then(1).otherwise(0).alias('stoch_bullish_crossover'),
                        pl.when((pl.col('stoch_k_regime') < pl.col('stoch_d_regime')) &
                               (pl.col('stoch_k_regime').shift(1) >= pl.col('stoch_d_regime').shift(1)))
                        .then(1).otherwise(0).alias('stoch_bearish_crossover')
                    ])
                else:
                    # Add default values if columns are missing
                    data = data.with_columns([
                        pl.lit(50.0).alias('stoch_k_regime'),
                        pl.lit(50.0).alias('stoch_d_regime'),
                        pl.lit(0).alias('stoch_bullish_crossover'),
                        pl.lit(0).alias('stoch_bearish_crossover')
                    ])
            except Exception as e:
                logger.warning(f"[WARNING] Failed to calculate Stochastic: {e}")
                # Add default values
                data = data.with_columns([
                    pl.lit(50.0).alias('stoch_k_regime'),
                    pl.lit(50.0).alias('stoch_d_regime'),
                    pl.lit(0).alias('stoch_bullish_crossover'),
                    pl.lit(0).alias('stoch_bearish_crossover')
                ])

            # Momentum regime classification
            data = data.with_columns([
                pl.when((pl.col('rsi_14_regime') > 70) & (pl.col('macd_slope') > 0)).then('STRONG_BULL_MOMENTUM')
                .when((pl.col('rsi_14_regime') < 30) & (pl.col('macd_slope') < 0)).then('STRONG_BEAR_MOMENTUM')
                .when(pl.col('rsi_14_regime').is_between(40, 60)).then('NEUTRAL_MOMENTUM')
                .otherwise('WEAK_MOMENTUM').alias('momentum_regime')
            ])

            # 4. Macro Flags (India VIX and FII/DII flow simulation)
            # Note: In production, these would come from external data sources
            data = data.with_columns([
                # Simulated India VIX (would be actual VIX data in production)
                (pl.col('annualized_vol') * 100).alias('india_vix_proxy'),
                # VIX regime classification
                pl.when(pl.col('annualized_vol') > 0.25).then('HIGH_VIX')
                .when(pl.col('annualized_vol') < 0.15).then('LOW_VIX')
                .otherwise('NORMAL_VIX').alias('vix_regime'),
                # Simulated FII/DII flow indicators (would be actual flow data in production)
                pl.when(pl.col('volume') > pl.col('volume').rolling_mean(window_size=20) * 1.5).then('HIGH_FLOW')
                .when(pl.col('volume') < pl.col('volume').rolling_mean(window_size=20) * 0.5).then('LOW_FLOW')
                .otherwise('NORMAL_FLOW').alias('institutional_flow_proxy')
            ])

            # Overall market regime classification
            data = data.with_columns([
                pl.when((pl.col('trend_alignment_bull') == 1) & (pl.col('volatility_regime') == 'LOW_VOL') &
                       (pl.col('momentum_regime') == 'STRONG_BULL_MOMENTUM')).then('BULL_MARKET')
                .when((pl.col('trend_alignment_bear') == 1) & (pl.col('volatility_regime') == 'HIGH_VOL') &
                     (pl.col('momentum_regime') == 'STRONG_BEAR_MOMENTUM')).then('BEAR_MARKET')
                .when(pl.col('volatility_regime') == 'HIGH_VOL').then('VOLATILE_MARKET')
                .when((pl.col('trend_alignment_bull') == 0) & (pl.col('trend_alignment_bear') == 0)).then('SIDEWAYS_MARKET')
                .otherwise('TRANSITIONAL_MARKET').alias('market_regime')
            ])

            # Select regime features
            regime_features = data.select([
                'timestamp', 'underlying',
                # Trend indicators
                'sma_10', 'sma_50', 'sma_200', 'ema_9', 'ema_21', 'ema_55',
                'macd_line', 'macd_signal_line', 'macd_slope',
                'bull_trend', 'bear_trend', 'trend_alignment_bull', 'trend_alignment_bear',
                # Volatility regime
                'rolling_vol', 'annualized_vol', 'volatility_regime', 'vol_ratio',
                # Momentum shifts
                'rsi_14_regime', 'stoch_k_regime', 'stoch_d_regime',
                'stoch_bullish_crossover', 'stoch_bearish_crossover', 'momentum_regime',
                # Macro flags
                'india_vix_proxy', 'vix_regime', 'institutional_flow_proxy',
                # Overall regime
                'market_regime'
            ]).drop_nulls()

            logger.info(f"[SUCCESS] Calculated market regime features: {regime_features.height} records")
            return regime_features

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate market regime features: {e}")
            return pl.DataFrame()

    async def _calculate_option_specific_features(self, data: pl.DataFrame, underlying: str) -> pl.DataFrame:
        """Calculate option-specific technical features"""
        try:
            logger.info(f"[OPTION-TECH] Calculating option-specific features for {underlying}...")

            if data.height == 0:
                return pl.DataFrame()

            # Group by expiry and strike for option-specific calculations
            option_features = []

            # Get unique expiry dates and strikes
            expiries = data['expiry_date'].unique().to_list() if 'expiry_date' in data.columns else ['2024-01-25']
            strikes = data['strike_price'].unique().to_list() if 'strike_price' in data.columns else []

            for expiry in expiries:
                expiry_data = data.filter(pl.col('expiry_date') == expiry) if 'expiry_date' in data.columns else data

                if expiry_data.height == 0:
                    continue

                # 1. Implied Volatility Analysis
                # Calculate IV rank and percentile
                if 'implied_vol' in expiry_data.columns:
                    iv_data = expiry_data.with_columns([
                        pl.col('implied_vol').rolling_quantile(0.8, window_size=30).alias('iv_80th_percentile'),
                        pl.col('implied_vol').rolling_quantile(0.2, window_size=30).alias('iv_20th_percentile'),
                        pl.col('implied_vol').rolling_mean(window_size=30).alias('iv_30d_mean')
                    ])

                    iv_data = iv_data.with_columns([
                        ((pl.col('implied_vol') - pl.col('iv_20th_percentile')) /
                         (pl.col('iv_80th_percentile') - pl.col('iv_20th_percentile'))).alias('iv_rank'),
                        (pl.col('implied_vol') / pl.col('iv_30d_mean')).alias('iv_percentile')
                    ])
                else:
                    # Create synthetic IV data if not available
                    iv_data = expiry_data.with_columns([
                        pl.lit(0.2).alias('implied_vol'),
                        pl.lit(0.5).alias('iv_rank'),
                        pl.lit(1.0).alias('iv_percentile')
                    ])

                # 2. IV Skew Calculation
                # Calculate skew between different strikes
                call_data = iv_data.filter(pl.col('option_type') == 'CE') if 'option_type' in iv_data.columns else iv_data
                put_data = iv_data.filter(pl.col('option_type') == 'PE') if 'option_type' in iv_data.columns else iv_data

                if call_data.height > 0 and put_data.height > 0:
                    # Calculate put-call IV skew
                    iv_data = iv_data.with_columns([
                        pl.when(pl.col('option_type') == 'CE')
                        .then(pl.col('implied_vol') - put_data['implied_vol'].mean())
                        .otherwise(pl.col('implied_vol') - call_data['implied_vol'].mean()).alias('iv_skew')
                    ])
                else:
                    iv_data = iv_data.with_columns([pl.lit(0.0).alias('iv_skew')])

                # 3. Enhanced Greeks Calculations
                if all(col in iv_data.columns for col in ['delta', 'gamma', 'theta', 'vega']):
                    greeks_enhanced = iv_data.with_columns([
                        # Delta-adjusted exposure
                        (pl.col('delta') * pl.col('open_interest') * pl.col('ltp')).alias('delta_exposure'),
                        # Gamma risk
                        (pl.col('gamma') * pl.col('open_interest') * pl.col('ltp')).alias('gamma_risk'),
                        # Theta decay rate
                        (pl.col('theta') / pl.col('ltp')).alias('theta_decay_rate'),
                        # Vega sensitivity
                        (pl.col('vega') * pl.col('open_interest')).alias('vega_exposure'),
                        # Greeks momentum
                        (pl.col('delta') - pl.col('delta').shift(1)).alias('delta_change'),
                        (pl.col('gamma') - pl.col('gamma').shift(1)).alias('gamma_change')
                    ])
                else:
                    # Create synthetic Greeks if not available
                    greeks_enhanced = iv_data.with_columns([
                        pl.lit(0.5).alias('delta_exposure'),
                        pl.lit(0.1).alias('gamma_risk'),
                        pl.lit(-0.01).alias('theta_decay_rate'),
                        pl.lit(0.2).alias('vega_exposure'),
                        pl.lit(0.0).alias('delta_change'),
                        pl.lit(0.0).alias('gamma_change')
                    ])

                # 4. PCR Analysis (Put-Call Ratio)
                # Index-level PCR
                total_call_volume = call_data['volume'].sum() if 'volume' in call_data.columns and call_data.height > 0 else 1
                total_put_volume = put_data['volume'].sum() if 'volume' in put_data.columns and put_data.height > 0 else 1
                total_call_oi = call_data['open_interest'].sum() if 'open_interest' in call_data.columns and call_data.height > 0 else 1
                total_put_oi = put_data['open_interest'].sum() if 'open_interest' in put_data.columns and put_data.height > 0 else 1

                pcr_volume = total_put_volume / total_call_volume if total_call_volume > 0 else 1.0
                pcr_oi = total_put_oi / total_call_oi if total_call_oi > 0 else 1.0

                # Strike-specific PCR
                greeks_enhanced = greeks_enhanced.with_columns([
                    pl.lit(pcr_volume).alias('pcr_volume_index'),
                    pl.lit(pcr_oi).alias('pcr_oi_index')
                ])

                # Calculate strike-specific PCR for each strike
                for strike in strikes[:10]:  # Limit to top 10 strikes
                    strike_calls = call_data.filter(pl.col('strike_price') == strike) if 'strike_price' in call_data.columns else pl.DataFrame()
                    strike_puts = put_data.filter(pl.col('strike_price') == strike) if 'strike_price' in put_data.columns else pl.DataFrame()

                    if strike_calls.height > 0 and strike_puts.height > 0:
                        strike_pcr_vol = strike_puts['volume'].sum() / strike_calls['volume'].sum() if strike_calls['volume'].sum() > 0 else 1.0
                        strike_pcr_oi = strike_puts['open_interest'].sum() / strike_calls['open_interest'].sum() if strike_calls['open_interest'].sum() > 0 else 1.0

                        greeks_enhanced = greeks_enhanced.with_columns([
                            pl.when(pl.col('strike_price') == strike)
                            .then(pl.lit(strike_pcr_vol))
                            .otherwise(pl.col('pcr_volume_index')).alias('pcr_volume_strike'),
                            pl.when(pl.col('strike_price') == strike)
                            .then(pl.lit(strike_pcr_oi))
                            .otherwise(pl.col('pcr_oi_index')).alias('pcr_oi_strike')
                        ])

                # 5. Open Interest Analysis
                if 'open_interest' in greeks_enhanced.columns:
                    # Use 'close' as proxy for 'ltp' if 'ltp' is not available
                    price_col = 'ltp' if 'ltp' in greeks_enhanced.columns else 'close'
                    oi_analysis = greeks_enhanced.with_columns([
                        # OI change analysis
                        (pl.col('open_interest') - pl.col('open_interest').shift(1)).alias('oi_change'),
                        (pl.col('open_interest') / pl.col('open_interest').shift(1) - 1).alias('oi_change_pct'),
                        # OI buildup/unwinding patterns
                        pl.when((pl.col('open_interest') > pl.col('open_interest').shift(1)) &
                               (pl.col(price_col) > pl.col(price_col).shift(1))).then('LONG_BUILDUP')
                        .when((pl.col('open_interest') > pl.col('open_interest').shift(1)) &
                             (pl.col(price_col) < pl.col(price_col).shift(1))).then('SHORT_BUILDUP')
                        .when((pl.col('open_interest') < pl.col('open_interest').shift(1)) &
                             (pl.col(price_col) > pl.col(price_col).shift(1))).then('SHORT_COVERING')
                        .when((pl.col('open_interest') < pl.col('open_interest').shift(1)) &
                             (pl.col(price_col) < pl.col(price_col).shift(1))).then('LONG_UNWINDING')
                        .otherwise('NO_PATTERN').alias('oi_pattern'),
                        # OI concentration
                        (pl.col('open_interest') / pl.col('open_interest').sum()).alias('oi_concentration')
                    ])
                else:
                    oi_analysis = greeks_enhanced.with_columns([
                        pl.lit(0).alias('oi_change'),
                        pl.lit(0.0).alias('oi_change_pct'),
                        pl.lit('NO_PATTERN').alias('oi_pattern'),
                        pl.lit(0.0).alias('oi_concentration')
                    ])

                # Add expiry-specific features
                oi_analysis = oi_analysis.with_columns([
                    pl.lit(expiry).alias('expiry_date_feature'),
                    pl.lit(underlying).alias('underlying_symbol')
                ])

                option_features.append(oi_analysis)

            # Combine all expiry data
            if option_features:
                combined_option_features = pl.concat(option_features)

                # Select final option-specific features
                final_features = combined_option_features.select([
                    'timestamp', 'underlying_symbol', 'expiry_date_feature',
                    # IV features
                    'implied_vol', 'iv_rank', 'iv_percentile', 'iv_skew',
                    # Enhanced Greeks
                    'delta_exposure', 'gamma_risk', 'theta_decay_rate', 'vega_exposure',
                    'delta_change', 'gamma_change',
                    # PCR features
                    'pcr_volume_index', 'pcr_oi_index', 'pcr_volume_strike', 'pcr_oi_strike',
                    # OI features
                    'oi_change', 'oi_change_pct', 'oi_pattern', 'oi_concentration'
                ]).drop_nulls()

                logger.info(f"[SUCCESS] Calculated option-specific features: {final_features.height} records")
                return final_features
            else:
                return pl.DataFrame()

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate option-specific features: {e}")
            return pl.DataFrame()

    async def _calculate_derived_quant_features(self, data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate derived quantitative features"""
        try:
            logger.info(f"[QUANT] Calculating derived quant features for {timeframe}...")

            if data.height < 30:  # Need sufficient data for quant analysis
                return pl.DataFrame()

            # 1. Return Statistics
            # Calculate various return periods
            data = data.with_columns([
                (pl.col('close').log() - pl.col('close').log().shift(1)).alias('daily_returns'),
                (pl.col('close').log() - pl.col('close').log().shift(5)).alias('weekly_returns'),
                (pl.col('close').log() - pl.col('close').log().shift(21)).alias('monthly_returns')
            ])

            # Rolling return statistics
            data = data.with_columns([
                pl.col('daily_returns').rolling_mean(window_size=5).alias('returns_5d_mean'),
                pl.col('daily_returns').rolling_std(window_size=5).alias('returns_5d_std'),
                pl.col('daily_returns').rolling_mean(window_size=21).alias('returns_21d_mean'),
                pl.col('daily_returns').rolling_std(window_size=21).alias('returns_21d_std'),
                pl.col('weekly_returns').rolling_mean(window_size=4).alias('weekly_returns_mean'),
                pl.col('monthly_returns').rolling_mean(window_size=3).alias('monthly_returns_mean')
            ])

            # Momentum scores
            data = data.with_columns([
                # Price momentum (rate of change)
                ((pl.col('close') / pl.col('close').shift(10)) - 1).alias('momentum_10d'),
                ((pl.col('close') / pl.col('close').shift(21)) - 1).alias('momentum_21d'),
                # Return momentum score
                (pl.col('returns_5d_mean') / pl.col('returns_5d_std')).alias('momentum_score_5d'),
                (pl.col('returns_21d_mean') / pl.col('returns_21d_std')).alias('momentum_score_21d')
            ])

            # 2. Sharpe Ratio Signals
            # Rolling Sharpe ratios (assuming risk-free rate = 0 for simplicity)
            data = data.with_columns([
                # 5-day rolling Sharpe (with null handling)
                (pl.col('returns_5d_mean') / pl.col('returns_5d_std').clip(lower_bound=0.001) * math.sqrt(252)).fill_null(0.0).alias('sharpe_5d'),
                # 21-day rolling Sharpe
                (pl.col('returns_21d_mean') / pl.col('returns_21d_std').clip(lower_bound=0.001) * math.sqrt(252)).fill_null(0.0).alias('sharpe_21d'),
                # 63-day rolling Sharpe (quarterly)
                (pl.col('daily_returns').rolling_mean(window_size=63) /
                 pl.col('daily_returns').rolling_std(window_size=63).clip(lower_bound=0.001) * math.sqrt(252)).fill_null(0.0).alias('sharpe_63d')
            ])

            # Sharpe ratio signals (with proper null handling)
            data = data.with_columns([
                pl.when(pl.col('sharpe_21d').is_null()).then(pl.lit('NEUTRAL_SHARPE'))
                .when(pl.col('sharpe_21d') > 1.0).then(pl.lit('HIGH_SHARPE'))
                .when(pl.col('sharpe_21d') < -1.0).then(pl.lit('LOW_SHARPE'))
                .otherwise(pl.lit('NEUTRAL_SHARPE')).alias('sharpe_signal'),
                # Sharpe momentum
                (pl.col('sharpe_21d') - pl.col('sharpe_21d').shift(5)).fill_null(0.0).alias('sharpe_momentum')
            ])

            # 3. Drawdown Profile
            # Calculate running maximum and drawdown (manual implementation)
            data = data.with_columns([
                pl.col('close').rolling_max(window_size=len(data), min_periods=1).alias('running_max'),
                pl.col('close').rolling_min(window_size=len(data), min_periods=1).alias('running_min')
            ])

            data = data.with_columns([
                # Current drawdown from peak
                ((pl.col('close') / pl.col('running_max')) - 1).alias('current_drawdown'),
                # Rolling max drawdown
                ((pl.col('close').rolling_min(window_size=21) /
                  pl.col('close').rolling_max(window_size=21)) - 1).alias('max_drawdown_21d'),
                ((pl.col('close').rolling_min(window_size=63) /
                  pl.col('close').rolling_max(window_size=63)) - 1).alias('max_drawdown_63d')
            ])

            # Drawdown recovery analysis
            data = data.with_columns([
                # Days since last high
                (pl.col('close') == pl.col('running_max')).cumsum().alias('days_since_high'),
                # Drawdown severity classification
                pl.when(pl.col('current_drawdown') < -0.10).then('SEVERE_DRAWDOWN')
                .when(pl.col('current_drawdown') < -0.05).then('MODERATE_DRAWDOWN')
                .when(pl.col('current_drawdown') < -0.02).then('MINOR_DRAWDOWN')
                .otherwise('NO_DRAWDOWN').alias('drawdown_severity')
            ])

            # 4. Profit Factor Trends
            # Calculate win/loss ratios over rolling windows
            data = data.with_columns([
                pl.when(pl.col('daily_returns') > 0).then(1).otherwise(0).alias('win_day'),
                pl.when(pl.col('daily_returns') < 0).then(1).otherwise(0).alias('loss_day'),
                pl.when(pl.col('daily_returns') > 0).then(pl.col('daily_returns')).otherwise(0).alias('win_return'),
                pl.when(pl.col('daily_returns') < 0).then(pl.col('daily_returns').abs()).otherwise(0).alias('loss_return')
            ])

            # Rolling win/loss statistics
            data = data.with_columns([
                # Win rate
                pl.col('win_day').rolling_mean(window_size=21).alias('win_rate_21d'),
                pl.col('win_day').rolling_mean(window_size=63).alias('win_rate_63d'),
                # Average win/loss
                pl.col('win_return').rolling_mean(window_size=21).alias('avg_win_21d'),
                pl.col('loss_return').rolling_mean(window_size=21).alias('avg_loss_21d'),
                # Profit factor (gross profit / gross loss)
                (pl.col('win_return').rolling_sum(window_size=21) /
                 pl.col('loss_return').rolling_sum(window_size=21)).alias('profit_factor_21d'),
                (pl.col('win_return').rolling_sum(window_size=63) /
                 pl.col('loss_return').rolling_sum(window_size=63)).alias('profit_factor_63d')
            ])

            # Profit factor trends
            data = data.with_columns([
                pl.when(pl.col('profit_factor_21d') > 1.5).then('HIGH_PROFIT_FACTOR')
                .when(pl.col('profit_factor_21d') < 0.8).then('LOW_PROFIT_FACTOR')
                .otherwise('NEUTRAL_PROFIT_FACTOR').alias('profit_factor_signal'),
                # Profit factor momentum
                (pl.col('profit_factor_21d') - pl.col('profit_factor_21d').shift(5)).alias('profit_factor_momentum')
            ])

            # 5. Additional Quant Metrics
            # Volatility-adjusted returns
            data = data.with_columns([
                (pl.col('daily_returns') / pl.col('returns_21d_std')).alias('vol_adjusted_return'),
                # Skewness and kurtosis proxies
                (pl.col('daily_returns') - pl.col('returns_21d_mean')).pow(3).rolling_mean(window_size=21).alias('skewness_proxy'),
                (pl.col('daily_returns') - pl.col('returns_21d_mean')).pow(4).rolling_mean(window_size=21).alias('kurtosis_proxy')
            ])

            # Risk-adjusted performance metrics
            data = data.with_columns([
                # Calmar ratio (annual return / max drawdown)
                (pl.col('returns_21d_mean') * 252 / pl.col('max_drawdown_21d').abs()).alias('calmar_ratio'),
                # Sortino ratio (downside deviation)
                (pl.col('returns_21d_mean') /
                 pl.col('daily_returns').filter(pl.col('daily_returns') < 0).rolling_std(window_size=21) *
                 math.sqrt(252)).alias('sortino_ratio')
            ])

            # Select quant features
            quant_features = data.select([
                'timestamp', 'underlying',
                # Return statistics
                'daily_returns', 'weekly_returns', 'monthly_returns',
                'returns_5d_mean', 'returns_21d_mean', 'momentum_10d', 'momentum_21d',
                'momentum_score_5d', 'momentum_score_21d',
                # Sharpe ratios
                'sharpe_5d', 'sharpe_21d', 'sharpe_63d', 'sharpe_signal', 'sharpe_momentum',
                # Drawdown metrics
                'current_drawdown', 'max_drawdown_21d', 'max_drawdown_63d',
                'days_since_high', 'drawdown_severity',
                # Profit factor
                'win_rate_21d', 'win_rate_63d', 'profit_factor_21d', 'profit_factor_63d',
                'profit_factor_signal', 'profit_factor_momentum',
                # Risk metrics
                'vol_adjusted_return', 'calmar_ratio', 'sortino_ratio',
                'skewness_proxy', 'kurtosis_proxy'
            ]).drop_nulls()

            logger.info(f"[SUCCESS] Calculated derived quant features: {quant_features.height} records")
            return quant_features

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate derived quant features: {e}")
            return pl.DataFrame()

    async def _calculate_advanced_features(self, options_data: pl.DataFrame, ohlc_data: pl.DataFrame, timeframe: str) -> pl.DataFrame:
        """Calculate advanced features: liquidity, timeframe-aware, strategy-specific, and meta features"""
        try:
            logger.info(f"[ADVANCED] Calculating advanced features for {timeframe}...")

            # Use OHLC data if available, otherwise options data
            data = ohlc_data if not ohlc_data.is_empty() else options_data

            if data.height < 10:
                return pl.DataFrame()

            # 4. Liquidity & Execution Features
            if 'bid' in data.columns and 'ask' in data.columns:
                # Use 'close' as proxy for 'ltp' if 'ltp' is not available
                price_col = 'ltp' if 'ltp' in data.columns else 'close'
                data = data.with_columns([
                    # Bid-ask spread
                    (pl.col('ask') - pl.col('bid')).alias('bid_ask_spread'),
                    ((pl.col('ask') - pl.col('bid')) / pl.col(price_col)).alias('bid_ask_spread_pct'),
                    # Slippage estimate
                    (pl.col('bid_ask_spread') * pl.col('volume').log()).alias('slippage_estimate')
                ])
            else:
                data = data.with_columns([
                    pl.lit(0.5).alias('bid_ask_spread'),
                    pl.lit(0.01).alias('bid_ask_spread_pct'),
                    pl.lit(0.02).alias('slippage_estimate')
                ])

            # Lot size efficiency (for Indian options, lot size varies by underlying)
            lot_sizes = {'NIFTY': 50, 'BANKNIFTY': 15, 'FINNIFTY': 40}  # Standard lot sizes
            # Extract underlying from symbol if 'underlying' column doesn't exist
            if 'underlying' in data.columns:
                underlying = data['underlying'].unique().to_list()[0]
            elif 'symbol' in data.columns:
                symbol = data['symbol'].unique().to_list()[0]
                if 'NIFTY' in symbol:
                    underlying = 'NIFTY'
                elif 'BANKNIFTY' in symbol:
                    underlying = 'BANKNIFTY'
                else:
                    underlying = 'NIFTY'
            else:
                underlying = 'NIFTY'
            lot_size = lot_sizes.get(underlying, 50)

            data = data.with_columns([
                pl.lit(lot_size).alias('lot_size'),
                (pl.col('ltp') * lot_size).alias('capital_per_lot'),
                (pl.col('daily_returns') * lot_size * pl.col('ltp')).alias('return_per_lot')
            ])

            # 5. Timeframe-Aware Features
            # Intraday patterns (hour-based analysis)
            if 'timestamp' in data.columns:
                data = data.with_columns([
                    pl.col('timestamp').dt.hour().alias('hour'),
                    pl.col('timestamp').dt.minute().alias('minute')
                ])

                # First hour range breakout
                data = data.with_columns([
                    pl.when(pl.col('hour') == 9).then(pl.col('high')).otherwise(None).alias('first_hour_high'),
                    pl.when(pl.col('hour') == 9).then(pl.col('low')).otherwise(None).alias('first_hour_low')
                ])

                # VWAP divergence
                if 'vwap' in data.columns:
                    data = data.with_columns([
                        ((pl.col('close') - pl.col('vwap')) / pl.col('vwap')).alias('vwap_divergence'),
                        pl.when(pl.col('close') > pl.col('vwap')).then(1).otherwise(0).alias('above_vwap')
                    ])

            # Time to expiry impact (for options)
            if 'expiry_date' in data.columns:
                data = data.with_columns([
                    pl.lit(7).alias('days_to_expiry'),  # Simplified - would calculate actual DTE
                    pl.when(pl.col('days_to_expiry') <= 1).then('EXPIRY_DAY')
                    .when(pl.col('days_to_expiry') <= 7).then('WEEKLY_EXPIRY')
                    .otherwise('MONTHLY_EXPIRY').alias('expiry_category')
                ])

            # 6. Strategy-Specific Tags
            # Bollinger Band analysis for breakout/reversal
            if 'bb_upper' in data.columns and 'bb_lower' in data.columns:
                data = data.with_columns([
                    pl.when(pl.col('close') >= pl.col('bb_upper')).then('BREAKOUT_UPPER')
                    .when(pl.col('close') <= pl.col('bb_lower')).then('BREAKOUT_LOWER')
                    .when(pl.col('close').is_between(pl.col('bb_lower'), pl.col('bb_upper'))).then('RANGE_BOUND')
                    .otherwise('NEUTRAL').alias('bb_signal'),
                    # BB width for squeeze detection
                    ((pl.col('bb_upper') - pl.col('bb_lower')) / pl.col('close')).alias('bb_width'),
                    pl.when(pl.col('bb_width') < pl.col('bb_width').rolling_quantile(0.2, window_size=20))
                    .then('SQUEEZE').otherwise('EXPANSION').alias('bb_squeeze')
                ])

            # Trend strength using ADX
            if 'adx' in data.columns:
                data = data.with_columns([
                    pl.when(pl.col('adx') > 25).then('STRONG_TREND')
                    .when(pl.col('adx') < 20).then('WEAK_TREND')
                    .otherwise('MODERATE_TREND').alias('trend_strength')
                ])

            # 7. Label-Aware Features (for supervised learning)
            # Future return targets
            data = data.with_columns([
                (pl.col('close').shift(-1) / pl.col('close') - 1).alias('next_1d_return'),
                (pl.col('close').shift(-5) / pl.col('close') - 1).alias('next_5d_return'),
                # Target labels
                pl.when(pl.col('next_1d_return') > 0.02).then('BUY_SIGNAL')
                .when(pl.col('next_1d_return') < -0.02).then('SELL_SIGNAL')
                .otherwise('HOLD_SIGNAL').alias('target_1d'),
                # Noise filtering
                pl.when(pl.col('volume') < pl.col('volume').rolling_mean(window_size=20) * 0.3)
                .then('LOW_VOLUME_NOISE').otherwise('NORMAL').alias('noise_filter')
            ])

            # 8. Meta-Feature Tracking
            # Feature importance proxies
            data = data.with_columns([
                # Correlation with future returns
                pl.corr(pl.col('rsi_14'), pl.col('next_1d_return')).alias('rsi_return_corr'),
                pl.corr(pl.col('macd'), pl.col('next_1d_return')).alias('macd_return_corr'),
                # Feature stability
                pl.col('rsi_14').rolling_std(window_size=10).alias('rsi_stability'),
                pl.col('macd').rolling_std(window_size=10).alias('macd_stability')
            ])

            # Select advanced features
            advanced_features = data.select([
                'timestamp', 'underlying',
                # Liquidity features
                'bid_ask_spread', 'bid_ask_spread_pct', 'slippage_estimate',
                'lot_size', 'capital_per_lot', 'return_per_lot',
                # Timeframe features
                'hour', 'vwap_divergence', 'above_vwap', 'expiry_category',
                # Strategy features
                'bb_signal', 'bb_width', 'bb_squeeze', 'trend_strength',
                # Label features
                'next_1d_return', 'next_5d_return', 'target_1d', 'noise_filter',
                # Meta features
                'rsi_return_corr', 'macd_return_corr', 'rsi_stability', 'macd_stability'
            ]).drop_nulls()

            logger.info(f"[SUCCESS] Calculated advanced features: {advanced_features.height} records")
            return advanced_features

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate advanced features: {e}")
            return pl.DataFrame()
    
    async def _calculate_options_flow(self, data: pl.DataFrame) -> pl.DataFrame:
        """Calculate options flow indicators"""
        try:
            logger.info("[FLOW] Calculating options flow indicators...")
            
            flow_data = []
            
            # Group by underlying and timestamp
            # Check if we have 'underlying' or need to extract from 'symbol'
            if 'underlying' in data.columns:
                grouped = data.group_by(['underlying', 'timestamp'])
            else:
                # Extract underlying from symbol and group
                data_with_underlying = data.with_columns([
                    pl.when(pl.col('symbol').str.contains('NIFTY'))
                    .then(pl.lit('NIFTY'))
                    .when(pl.col('symbol').str.contains('BANKNIFTY'))
                    .then(pl.lit('BANKNIFTY'))
                    .otherwise(pl.col('symbol'))
                    .alias('underlying')
                ])
                grouped = data_with_underlying.group_by(['underlying', 'timestamp'])
            
            for (underlying, timestamp), group_data in grouped:
                # Calculate Put-Call ratio
                calls = group_data.filter(pl.col('option_type') == 'CE')
                puts = group_data.filter(pl.col('option_type') == 'PE')
                
                call_volume = calls['volume'].sum() if calls.height > 0 and 'volume' in calls.columns else 0
                put_volume = puts['volume'].sum() if puts.height > 0 and 'volume' in puts.columns else 0

                # Handle open_interest column if it exists, otherwise use default values
                if 'open_interest' in calls.columns:
                    call_oi = calls['open_interest'].sum() if calls.height > 0 else 0
                    put_oi = puts['open_interest'].sum() if puts.height > 0 else 0
                else:
                    # Use volume as proxy for open interest if not available
                    call_oi = call_volume
                    put_oi = put_volume
                
                pcr_volume = put_volume / call_volume if call_volume > 0 else 0
                pcr_oi = put_oi / call_oi if call_oi > 0 else 0
                
                # Calculate max pain
                max_pain = self._calculate_max_pain(group_data)
                
                # Options flow sentiment
                total_volume = call_volume + put_volume
                call_percentage = call_volume / total_volume if total_volume > 0 else 0.5
                
                flow_data.append({
                    'underlying': underlying,
                    'timestamp': timestamp,
                    'pcr_volume': pcr_volume,
                    'pcr_oi': pcr_oi,
                    'call_volume': call_volume,
                    'put_volume': put_volume,
                    'call_oi': call_oi,
                    'put_oi': put_oi,
                    'total_volume': total_volume,
                    'call_percentage': call_percentage,
                    'max_pain': max_pain
                })
            
            flow_df = pl.DataFrame(flow_data)
            logger.info(f"[SUCCESS] Calculated options flow for {len(flow_data)} records")
            
            return flow_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate options flow: {e}")
            return pl.DataFrame()
    
    def _calculate_max_pain(self, options_data: pl.DataFrame) -> float:
        """Calculate max pain point using polars"""
        try:
            # Ensure open_interest column exists
            if 'open_interest' not in options_data.columns:
                options_data = options_data.with_columns([
                    (pl.col('volume') * 1.5).alias('open_interest')  # Use volume as proxy
                ])

            # Get unique strike prices
            strikes = options_data['strike_price'].unique().sort().to_list()

            if not strikes:
                return 0.0

            max_pain_strike = strikes[0]
            min_pain = float('inf')

            for strike in strikes:
                # Calculate pain for calls using polars
                calls_pain = options_data.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') <= strike)
                ).with_columns([
                    ((pl.lit(strike) - pl.col('strike_price')) * pl.col('open_interest')).alias('call_pain')
                ]).select([
                    pl.when(pl.col('call_pain') > 0).then(pl.col('call_pain')).otherwise(0).alias('call_pain')
                ])['call_pain'].sum()

                # Calculate pain for puts using polars
                puts_pain = options_data.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') >= strike)
                ).with_columns([
                    ((pl.col('strike_price') - pl.lit(strike)) * pl.col('open_interest')).alias('put_pain')
                ]).select([
                    pl.when(pl.col('put_pain') > 0).then(pl.col('put_pain')).otherwise(0).alias('put_pain')
                ])['put_pain'].sum()

                total_pain = (calls_pain or 0) + (puts_pain or 0)

                if total_pain < min_pain:
                    min_pain = total_pain
                    max_pain_strike = strike

            return max_pain_strike

        except Exception as e:
            logger.warning(f"[WARNING] Failed to calculate max pain: {e}")
            return 0.0
    
    async def _combine_features(self, historical_data: pl.DataFrame, greeks_data: pl.DataFrame,
                              volatility_data: pl.DataFrame, technical_features: pl.DataFrame,
                              flow_indicators: pl.DataFrame, regime_features: pl.DataFrame = None,
                              option_tech_features: pl.DataFrame = None, quant_features: pl.DataFrame = None,
                              advanced_features: pl.DataFrame = None) -> pl.DataFrame:
        """Combine all features into a single dataset"""
        try:
            logger.info("[COMBINE] Combining all features...")

            # Start with historical data and ensure we have an 'underlying' column
            combined = historical_data
            if 'underlying' not in combined.columns and 'symbol' in combined.columns:
                combined = combined.with_columns([
                    pl.when(pl.col('symbol').str.contains('NIFTY'))
                    .then(pl.lit('NIFTY'))
                    .when(pl.col('symbol').str.contains('BANKNIFTY'))
                    .then(pl.lit('BANKNIFTY'))
                    .otherwise(pl.col('symbol'))
                    .alias('underlying')
                ])

            # Determine join columns based on available columns
            join_cols = ['timestamp']
            if 'symbol' in combined.columns:
                join_cols.append('symbol')
            if 'underlying' in combined.columns:
                join_cols.append('underlying')

            # Join Greeks data
            if greeks_data.height > 0:
                # Ensure Greeks data has the same columns
                if 'underlying' not in greeks_data.columns and 'symbol' in greeks_data.columns:
                    greeks_data = greeks_data.with_columns([
                        pl.when(pl.col('symbol').str.contains('NIFTY'))
                        .then(pl.lit('NIFTY'))
                        .when(pl.col('symbol').str.contains('BANKNIFTY'))
                        .then(pl.lit('BANKNIFTY'))
                        .otherwise(pl.col('symbol'))
                        .alias('underlying')
                    ])

                available_join_cols = [col for col in join_cols if col in greeks_data.columns]
                if available_join_cols:
                    combined = combined.join(greeks_data, on=available_join_cols, how='left')

            # Join volatility data
            if volatility_data.height > 0:
                available_join_cols = [col for col in join_cols if col in volatility_data.columns]
                if available_join_cols:
                    combined = combined.join(volatility_data, on=available_join_cols, how='left')

            # Join technical features
            if technical_features.height > 0:
                available_join_cols = [col for col in join_cols if col in technical_features.columns]
                if available_join_cols:
                    combined = combined.join(technical_features, on=available_join_cols, how='left')

            # Join flow indicators
            if flow_indicators.height > 0:
                # Fix timezone mismatch by ensuring consistent timezone (keep IST)
                if 'timestamp' in flow_indicators.columns and 'timestamp' in combined.columns:
                    # Convert both to naive datetime to avoid timezone mismatch
                    flow_indicators = flow_indicators.with_columns([
                        pl.col('timestamp').dt.replace_time_zone(None).alias('timestamp')
                    ])
                    combined = combined.with_columns([
                        pl.col('timestamp').dt.replace_time_zone(None).alias('timestamp')
                    ])

                available_join_cols = [col for col in join_cols if col in flow_indicators.columns]
                if available_join_cols:
                    combined = combined.join(flow_indicators, on=available_join_cols, how='left')

            # Join regime features
            if regime_features is not None and regime_features.height > 0:
                available_join_cols = [col for col in join_cols if col in regime_features.columns]
                if available_join_cols:
                    combined = combined.join(regime_features, on=available_join_cols, how='left')

            # Join option-specific features
            if option_tech_features is not None and option_tech_features.height > 0:
                # Rename column if needed
                opt_features = option_tech_features
                if 'underlying_symbol' in opt_features.columns:
                    opt_features = opt_features.rename({'underlying_symbol': 'underlying'})
                available_join_cols = [col for col in join_cols if col in opt_features.columns]
                if available_join_cols:
                    combined = combined.join(opt_features, on=available_join_cols, how='left')

            # Join quant features
            if quant_features is not None and quant_features.height > 0:
                available_join_cols = [col for col in join_cols if col in quant_features.columns]
                if available_join_cols:
                    combined = combined.join(quant_features, on=available_join_cols, how='left')

            # Join advanced features
            if advanced_features is not None and advanced_features.height > 0:
                combined = combined.join(
                    advanced_features,
                    on=['underlying', 'timestamp'],
                    how='left'
                )

            logger.info(f"[SUCCESS] Combined all features: {combined.height} rows, {len(combined.columns)} columns")

            return combined
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to combine features: {e}")
            return historical_data
    
    async def _save_timeframe_features(self, underlying: str, timeframe: str, features_data: pl.DataFrame):
        """Save engineered features for specific timeframe"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # Save main features
            features_file = self.features_path / timeframe / f"{underlying}_{timeframe}_features_{timestamp}.parquet"
            features_data.write_parquet(features_file, compression="snappy")

            # Save Greeks separately
            if 'delta' in features_data.columns:
                greeks_cols = ['symbol', 'timestamp', 'timeframe', 'delta', 'gamma', 'theta', 'vega', 'rho', 'implied_vol']
                greeks_data = features_data.select([col for col in greeks_cols if col in features_data.columns])
                greeks_file = self.greeks_path / timeframe / f"{underlying}_{timeframe}_greeks_{timestamp}.parquet"
                greeks_data.write_parquet(greeks_file, compression="snappy")

            # Save volatility data separately
            if 'historical_vol' in features_data.columns:
                vol_cols = ['symbol', 'timestamp', 'timeframe', 'historical_vol', 'current_vol', 'vol_rank', 'vol_percentile']
                vol_data = features_data.select([col for col in vol_cols if col in features_data.columns])
                vol_file = self.volatility_path / timeframe / f"{underlying}_{timeframe}_volatility_{timestamp}.parquet"
                vol_data.write_parquet(vol_file, compression="snappy")

            # Save comprehensive features separately by category
            if features_data.height > 0:
                # Technical features
                tech_cols = [
                    'underlying', 'timestamp', 'timeframe',
                    # Basic OHLCV
                    'open', 'high', 'low', 'close', 'volume',
                    # RSI variants
                    'rsi_5', 'rsi_14', 'rsi_14_regime',
                    # Moving averages
                    'sma_10', 'sma_20', 'sma_50', 'sma_200',
                    'ema_5', 'ema_9', 'ema_10', 'ema_12', 'ema_13', 'ema_20', 'ema_21',
                    'ema_30', 'ema_50', 'ema_55', 'ema_100',
                    # Bollinger Bands
                    'bb_upper', 'bb_lower', 'bb_middle', 'bb_width', 'bb_squeeze', 'bb_signal',
                    # Momentum indicators
                    'atr', 'adx', 'volume_sma', 'price_change', 'volume_ratio',
                    'macd', 'macd_signal', 'macd_hist', 'macd_line', 'macd_signal_line', 'macd_slope',
                    'stoch_k', 'stoch_d', 'stoch_k_regime', 'stoch_d_regime',
                    'stoch_bullish_crossover', 'stoch_bearish_crossover',
                    'williams_r', 'cci', 'mfi',
                    # Advanced indicators
                    'vwap', 'vwap_divergence', 'above_vwap', 'supertrend',
                    'donchian_high', 'donchian_low',
                    'pivot', 'support', 'resistance', 'cpr_top', 'cpr_bottom'
                ]
                tech_data = features_data.select([col for col in tech_cols if col in features_data.columns])
                if tech_data.height > 0:
                    tech_file = self.features_path / timeframe / f"{underlying}_{timeframe}_technical_{timestamp}.parquet"
                    tech_data.write_parquet(tech_file, compression="snappy")

                # Market regime features
                regime_cols = [
                    'underlying', 'timestamp', 'bull_trend', 'bear_trend',
                    'trend_alignment_bull', 'trend_alignment_bear', 'volatility_regime',
                    'momentum_regime', 'market_regime', 'vix_regime', 'trend_strength'
                ]
                regime_data = features_data.select([col for col in regime_cols if col in features_data.columns])
                if regime_data.height > 0:
                    regime_file = self.features_path / timeframe / f"{underlying}_{timeframe}_regime_{timestamp}.parquet"
                    regime_data.write_parquet(regime_file, compression="snappy")

                # Quant features
                quant_cols = [
                    'underlying', 'timestamp', 'daily_returns', 'weekly_returns', 'monthly_returns',
                    'sharpe_5d', 'sharpe_21d', 'sharpe_63d', 'sharpe_signal',
                    'current_drawdown', 'max_drawdown_21d', 'drawdown_severity',
                    'profit_factor_21d', 'profit_factor_signal', 'win_rate_21d',
                    'calmar_ratio', 'sortino_ratio'
                ]
                quant_data = features_data.select([col for col in quant_cols if col in features_data.columns])
                if quant_data.height > 0:
                    quant_file = self.features_path / timeframe / f"{underlying}_{timeframe}_quant_{timestamp}.parquet"
                    quant_data.write_parquet(quant_file, compression="snappy")

                # Option-specific features
                option_cols = [
                    'underlying', 'timestamp', 'implied_vol', 'iv_rank', 'iv_percentile', 'iv_skew',
                    'delta_exposure', 'gamma_risk', 'theta_decay_rate', 'vega_exposure',
                    'pcr_volume_index', 'pcr_oi_index', 'oi_change', 'oi_pattern'
                ]
                option_data = features_data.select([col for col in option_cols if col in features_data.columns])
                if option_data.height > 0:
                    option_file = self.features_path / timeframe / f"{underlying}_{timeframe}_options_{timestamp}.parquet"
                    option_data.write_parquet(option_file, compression="snappy")

            logger.info(f"[SAVE] {timeframe} features saved for {underlying}: {features_data.height} records")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save {timeframe} features for {underlying}: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Feature Engineering Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options Feature Engineering Agent cleaned up")
            
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    """Example usage of Options Feature Engineering Agent"""
    agent = OptionsFeatureEngineeringAgent()
    
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python3
"""
Options Feature Engineering Agent - Greeks, Volatility & Technical Features

Features:
ðŸ“Š 1. Options Greeks Calculation
- Delta, Gamma, Theta, Vega, Rho calculations
- Greeks sensitivity analysis
- Portfolio Greeks aggregation
- Greeks-based risk metrics

ðŸ“ˆ 2. Volatility Modeling
- Implied volatility calculation
- Historical volatility analysis
- Volatility surface construction
- Volatility smile/skew analysis

âš¡ 3. Technical Features
- Options flow indicators
- Put-Call ratio analysis
- Open interest analysis
- Volume-weighted metrics

ðŸŽ¯ 4. High-Performance Processing
- Vectorized Greeks calculations
- Polars + PyArrow optimization
- GPU-accelerated computations
- Memory-efficient processing
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import aiofiles
from dataclasses import dataclass
import json
import math

# Options pricing libraries
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
    from py_vollib.black_scholes.implied_volatility import implied_volatility
except ImportError:
    logger.warning("py_vollib not installed. Using fallback calculations.")

# Use polars-talib for technical indicators
import polars_talib as ta

logger = logging.getLogger(__name__)

@dataclass
class GreeksData:
    """Greeks calculation results"""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    implied_vol: float

@dataclass
class VolatilityMetrics:
    """Volatility analysis results"""
    historical_vol: float
    implied_vol: float
    vol_rank: float
    vol_percentile: float
    vol_skew: float

class OptionsFeatureEngineeringAgent:
    """
    Options Feature Engineering Agent for calculating Greeks, volatility, and technical features
    
    Handles:
    - Options Greeks calculations (Delta, Gamma, Theta, Vega, Rho)
    - Implied and historical volatility analysis
    - Volatility surface construction
    - Options flow and sentiment indicators
    - Technical analysis on underlying assets
    """
    
    def __init__(self, config_path: str = "config/options_feature_engineering_config.yaml"):
        """Initialize Options Feature Engineering Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths for multi-timeframe processing
        self.data_path = Path("data")
        self.historical_path = self.data_path / "historical"
        self.live_path = self.data_path / "live"
        self.features_path = self.data_path / "features"
        self.greeks_path = self.data_path / "greeks"
        self.volatility_path = self.data_path / "volatility"

        # Multi-timeframe support
        self.timeframes = ["1min", "3min", "5min", "15min"]

        # Create directories for each timeframe
        for timeframe in self.timeframes:
            (self.features_path / timeframe).mkdir(parents=True, exist_ok=True)
            (self.greeks_path / timeframe).mkdir(parents=True, exist_ok=True)
            (self.volatility_path / timeframe).mkdir(parents=True, exist_ok=True)
        
        # Risk-free rate (can be updated from market data)
        self.risk_free_rate = 0.06  # 6% annual
        
        logger.info("[INIT] Options Feature Engineering Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            # Load configuration
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            logger.info("[SUCCESS] Options Feature Engineering Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from file"""
        try:
            # Default configuration
            self.config = {
                'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
                'lookback_days': 30,
                'volatility_window': 20,
                'risk_free_rate': 0.06,
                'chunk_size': 10000
            }
            logger.info("[CONFIG] Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise


if __name__ == "__main__":
    # Demo usage
    import asyncio
    
    async def main():
        agent = OptionsFeatureEngineeringAgent()
        await agent.start()
    
    asyncio.run(main())
