#!/usr/bin/env python3
"""
Enhanced Download Manager with Multiprocessing and Retry Mechanisms

Features:
🚀 1. Multiprocessing Support
- Parallel downloads with configurable worker processes
- Shared rate limiting across all processes
- Efficient batch processing

⚡ 2. Advanced Rate Limiting
- 90 API calls per minute limit (configurable)
- Distributed rate limiting across processes
- Smart sleep timing (0.4s minimum between calls)

🔄 3. Comprehensive Retry Mechanism
- Exponential backoff for failed downloads
- Different retry strategies for different error types
- Failed download tracking and retry after completion

📊 4. Progress Tracking
- Real-time progress monitoring
- Success/failure statistics
- Detailed logging and metrics
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, asdict
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from multiprocessing import Manager, Queue, Value, Lock
import polars as pl
import threading
import queue
import traceback

logger = logging.getLogger(__name__)

@dataclass
class DownloadTask:
    """Represents a single download task"""
    task_id: str
    symbol: str
    token: str
    exchange: str
    underlying: str
    strike_price: float
    option_type: str
    start_date: str
    end_date: str
    interval: str = "ONE_MINUTE"
    priority: int = 1
    retry_count: int = 0
    max_retries: int = 3
    last_error: Optional[str] = None
    created_at: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()

@dataclass
class DownloadResult:
    """Represents the result of a download task"""
    task_id: str
    success: bool
    data: Optional[List[Dict]] = None
    error: Optional[str] = None
    retry_count: int = 0
    download_time: float = 0.0
    api_calls_made: int = 1

@dataclass
class DownloadStats:
    """Download statistics tracking"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    retried_tasks: int = 0
    total_api_calls: int = 0
    total_download_time: float = 0.0
    start_time: Optional[str] = None
    end_time: Optional[str] = None

class RateLimiter:
    """Thread-safe rate limiter for API calls"""
    
    def __init__(self, max_calls_per_minute: int = 90, min_sleep_time: float = 0.4):
        self.max_calls_per_minute = max_calls_per_minute
        self.min_sleep_time = min_sleep_time
        self.call_times = []
        self.lock = threading.Lock()
        
    def wait_if_needed(self):
        """Wait if necessary to respect rate limits"""
        with self.lock:
            current_time = time.time()
            
            # Remove calls older than 1 minute
            cutoff_time = current_time - 60
            self.call_times = [t for t in self.call_times if t > cutoff_time]
            
            # Check if we need to wait
            if len(self.call_times) >= self.max_calls_per_minute:
                # Wait until the oldest call is more than 1 minute old
                oldest_call = min(self.call_times)
                wait_time = 60 - (current_time - oldest_call) + 0.1  # Small buffer
                if wait_time > 0:
                    logger.info(f"[RATE LIMIT] Waiting {wait_time:.2f}s to respect API limits")
                    time.sleep(wait_time)
                    current_time = time.time()
            
            # Always enforce minimum sleep time
            if self.call_times and (current_time - max(self.call_times)) < self.min_sleep_time:
                sleep_time = self.min_sleep_time - (current_time - max(self.call_times))
                time.sleep(sleep_time)
                current_time = time.time()
            
            # Record this call
            self.call_times.append(current_time)

class EnhancedDownloadManager:
    """Enhanced download manager with multiprocessing and retry mechanisms"""
    
    def __init__(self, 
                 smart_api,
                 max_workers: int = 5,
                 max_calls_per_minute: int = 90,
                 min_sleep_time: float = 0.4,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 exponential_backoff: bool = True):
        """
        Initialize Enhanced Download Manager
        
        Args:
            smart_api: SmartAPI instance for making API calls
            max_workers: Number of worker processes (5-10 recommended)
            max_calls_per_minute: API rate limit (90 for SmartAPI)
            min_sleep_time: Minimum time between API calls (0.4s)
            max_retries: Maximum retry attempts per task
            retry_delay: Base delay for retries
            exponential_backoff: Use exponential backoff for retries
        """
        self.smart_api = smart_api
        self.max_workers = max_workers
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.exponential_backoff = exponential_backoff
        
        # Rate limiting
        self.rate_limiter = RateLimiter(max_calls_per_minute, min_sleep_time)
        
        # Task management
        self.download_queue = queue.Queue()
        self.retry_queue = queue.Queue()
        self.results = {}
        self.failed_tasks = []
        
        # Statistics
        self.stats = DownloadStats()
        
        # Progress tracking
        self.progress_callback: Optional[Callable] = None
        
        logger.info(f"[INIT] Enhanced Download Manager initialized with {max_workers} workers")
        logger.info(f"[INIT] Rate limit: {max_calls_per_minute} calls/min, min sleep: {min_sleep_time}s")
    
    def set_progress_callback(self, callback: Callable[[int, int, int], None]):
        """Set callback for progress updates (completed, total, failed)"""
        self.progress_callback = callback
    
    def add_download_task(self, task: DownloadTask):
        """Add a download task to the queue"""
        self.download_queue.put(task)
        self.stats.total_tasks += 1
    
    def add_download_tasks(self, tasks: List[DownloadTask]):
        """Add multiple download tasks to the queue"""
        for task in tasks:
            self.add_download_task(task)
    
    def _download_single_task(self, task: DownloadTask) -> DownloadResult:
        """Download data for a single task with retry logic"""
        start_time = time.time()
        
        try:
            # Wait for rate limiting
            self.rate_limiter.wait_if_needed()
            
            # Prepare API parameters
            historic_param = {
                "exchange": task.exchange,
                "symboltoken": task.token,
                "interval": task.interval,
                "fromdate": task.start_date,
                "todate": task.end_date
            }
            
            # Make API call
            logger.debug(f"[DOWNLOAD] Downloading {task.symbol} ({task.task_id})")
            hist_data = self.smart_api.getCandleData(historic_param)
            
            download_time = time.time() - start_time
            
            if hist_data and hist_data.get('status') and hist_data.get('data'):
                # Convert to standardized format
                data_list = []
                for candle in hist_data['data']:
                    data_list.append({
                        'timestamp': candle[0],
                        'open': float(candle[1]),
                        'high': float(candle[2]),
                        'low': float(candle[3]),
                        'close': float(candle[4]),
                        'volume': int(candle[5]),
                        'underlying': task.underlying,
                        'strike_price': task.strike_price,
                        'option_type': task.option_type,
                        'symbol': task.symbol
                    })
                
                return DownloadResult(
                    task_id=task.task_id,
                    success=True,
                    data=data_list,
                    retry_count=task.retry_count,
                    download_time=download_time
                )
            else:
                error_msg = hist_data.get('message', 'No data returned') if hist_data else 'API call failed'
                return DownloadResult(
                    task_id=task.task_id,
                    success=False,
                    error=error_msg,
                    retry_count=task.retry_count,
                    download_time=download_time
                )
                
        except Exception as e:
            download_time = time.time() - start_time
            error_msg = str(e)
            
            return DownloadResult(
                task_id=task.task_id,
                success=False,
                error=error_msg,
                retry_count=task.retry_count,
                download_time=download_time
            )
    
    def _should_retry(self, result: DownloadResult, task: DownloadTask) -> bool:
        """Determine if a failed task should be retried"""
        if result.success or task.retry_count >= self.max_retries:
            return False
        
        # Check error type for retry decision
        if result.error:
            error_lower = result.error.lower()
            
            # Always retry these errors
            retry_errors = [
                'timeout', 'connection', 'network', 'temporary',
                'rate limit', 'too many requests', 'ab1004',
                'something went wrong', 'internal server error'
            ]
            
            if any(err in error_lower for err in retry_errors):
                return True
            
            # Don't retry these errors
            no_retry_errors = [
                'invalid token', 'unauthorized', 'forbidden',
                'not found', 'invalid symbol', 'invalid date'
            ]
            
            if any(err in error_lower for err in no_retry_errors):
                return False
        
        # Default: retry if under max retries
        return True
    
    def _calculate_retry_delay(self, retry_count: int) -> float:
        """Calculate delay before retry with exponential backoff"""
        if self.exponential_backoff:
            return self.retry_delay * (2 ** retry_count)
        else:
            return self.retry_delay
    
    async def download_all_tasks(self) -> Dict[str, DownloadResult]:
        """Download all tasks using threading with proper rate limiting and retry mechanism"""
        self.stats.start_time = datetime.now().isoformat()
        logger.info(f"[START] Starting download of {self.stats.total_tasks} tasks with {self.max_workers} workers")

        # Convert queue to list for processing
        tasks_to_process = []
        while not self.download_queue.empty():
            tasks_to_process.append(self.download_queue.get())

        # Process tasks in smaller batches to respect rate limits
        # Calculate batch size based on rate limits
        calls_per_batch = min(self.max_workers, self.rate_limiter.max_calls_per_minute // 4)  # Conservative batching
        batch_size = max(1, calls_per_batch)
        task_batches = [tasks_to_process[i:i + batch_size] for i in range(0, len(tasks_to_process), batch_size)]

        logger.info(f"[BATCH] Processing {len(task_batches)} batches with ~{batch_size} tasks each")

        # Process all batches with delays between batches
        for batch_num, batch in enumerate(task_batches, 1):
            logger.info(f"[BATCH {batch_num}/{len(task_batches)}] Processing {len(batch)} tasks")
            await self._process_batch(batch)

            # Update progress
            if self.progress_callback:
                self.progress_callback(self.stats.completed_tasks, self.stats.total_tasks, self.stats.failed_tasks)

            # Add delay between batches to help with rate limiting
            if batch_num < len(task_batches):
                batch_delay = max(0.5, len(batch) * self.rate_limiter.min_sleep_time)
                await asyncio.sleep(batch_delay)

        # Process retry queue
        await self._process_retries()

        self.stats.end_time = datetime.now().isoformat()
        self._log_final_stats()

        return self.results

    async def _process_batch(self, batch: List[DownloadTask]):
        """Process a batch of tasks with threading for I/O concurrency"""
        import concurrent.futures

        # Use ThreadPoolExecutor for I/O bound tasks (API calls)
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks in the batch
            future_to_task = {
                executor.submit(self._download_single_task, task): task
                for task in batch
            }

            # Process completed tasks
            for future in concurrent.futures.as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    await self._handle_task_result(task, result)
                except Exception as e:
                    logger.error(f"[ERROR] Exception in task {task.task_id}: {e}")
                    error_result = DownloadResult(
                        task_id=task.task_id,
                        success=False,
                        error=str(e),
                        retry_count=task.retry_count
                    )
                    await self._handle_task_result(task, error_result)

    async def _handle_task_result(self, task: DownloadTask, result: DownloadResult):
        """Handle the result of a download task"""
        self.results[task.task_id] = result
        self.stats.total_api_calls += result.api_calls_made
        self.stats.total_download_time += result.download_time

        if result.success:
            self.stats.completed_tasks += 1
            logger.debug(f"[SUCCESS] {task.symbol} downloaded successfully ({len(result.data)} records)")
        else:
            # Check if we should retry
            if self._should_retry(result, task):
                task.retry_count += 1
                task.last_error = result.error
                self.retry_queue.put(task)
                self.stats.retried_tasks += 1
                logger.warning(f"[RETRY] {task.symbol} failed, queued for retry {task.retry_count}/{self.max_retries}: {result.error}")
            else:
                self.stats.failed_tasks += 1
                self.failed_tasks.append(task)
                logger.error(f"[FAILED] {task.symbol} failed permanently: {result.error}")

    async def _process_retries(self):
        """Process all tasks in the retry queue"""
        if self.retry_queue.empty():
            return

        retry_tasks = []
        while not self.retry_queue.empty():
            retry_tasks.append(self.retry_queue.get())

        if not retry_tasks:
            return

        logger.info(f"[RETRY] Processing {len(retry_tasks)} retry tasks")

        # Sort by retry count (process lower retry counts first)
        retry_tasks.sort(key=lambda t: t.retry_count)

        # Process retries in smaller batches with delays
        retry_batch_size = max(1, self.max_workers // 2)  # Smaller batches for retries

        for i in range(0, len(retry_tasks), retry_batch_size):
            batch = retry_tasks[i:i + retry_batch_size]

            # Calculate delay based on highest retry count in batch
            max_retry_count = max(task.retry_count for task in batch)
            delay = self._calculate_retry_delay(max_retry_count)

            if delay > 0:
                logger.info(f"[RETRY] Waiting {delay:.2f}s before retry batch")
                await asyncio.sleep(delay)

            await self._process_batch(batch)

    def _log_final_stats(self):
        """Log final download statistics"""
        duration = 0
        if self.stats.start_time and self.stats.end_time:
            start = datetime.fromisoformat(self.stats.start_time)
            end = datetime.fromisoformat(self.stats.end_time)
            duration = (end - start).total_seconds()

        success_rate = (self.stats.completed_tasks / self.stats.total_tasks * 100) if self.stats.total_tasks > 0 else 0
        avg_download_time = (self.stats.total_download_time / self.stats.total_api_calls) if self.stats.total_api_calls > 0 else 0

        logger.info("=" * 60)
        logger.info("[DOWNLOAD STATISTICS]")
        logger.info("=" * 60)
        logger.info(f"Total Tasks: {self.stats.total_tasks}")
        logger.info(f"Completed: {self.stats.completed_tasks}")
        logger.info(f"Failed: {self.stats.failed_tasks}")
        logger.info(f"Retried: {self.stats.retried_tasks}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        logger.info(f"Total Duration: {duration:.1f}s")
        logger.info(f"Total API Calls: {self.stats.total_api_calls}")
        logger.info(f"Average API Call Time: {avg_download_time:.2f}s")
        logger.info(f"API Calls per Minute: {(self.stats.total_api_calls / (duration / 60)):.1f}" if duration > 0 else "N/A")
        logger.info("=" * 60)

    def get_failed_tasks(self) -> List[DownloadTask]:
        """Get list of permanently failed tasks"""
        return self.failed_tasks.copy()

    def get_statistics(self) -> DownloadStats:
        """Get current download statistics"""
        return self.stats

    def save_results_to_files(self, output_path: Path, file_format: str = "parquet"):
        """Save successful download results to files"""
        output_path.mkdir(parents=True, exist_ok=True)
        saved_files = []

        for task_id, result in self.results.items():
            if result.success and result.data:
                try:
                    # Convert to Polars DataFrame
                    df = pl.DataFrame(result.data)

                    # Generate filename from task data
                    first_record = result.data[0]
                    underlying = first_record['underlying']
                    strike_price = int(first_record['strike_price'])
                    option_type = first_record['option_type']
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

                    if file_format.lower() == "parquet":
                        filename = f"{underlying}_{strike_price}_{option_type}_1MIN_{timestamp}.parquet"
                        filepath = output_path / filename
                        df.write_parquet(filepath, compression="brotli")
                    elif file_format.lower() == "csv":
                        filename = f"{underlying}_{strike_price}_{option_type}_1MIN_{timestamp}.csv"
                        filepath = output_path / filename
                        df.write_csv(filepath)
                    else:
                        logger.warning(f"[WARNING] Unsupported file format: {file_format}")
                        continue

                    saved_files.append(filepath)
                    logger.debug(f"[SAVE] Saved {len(result.data)} records to {filename}")

                except Exception as e:
                    logger.error(f"[ERROR] Failed to save results for task {task_id}: {e}")

        logger.info(f"[SAVE] Saved {len(saved_files)} files to {output_path}")
        return saved_files

    def save_failed_tasks_report(self, output_path: Path):
        """Save a report of failed tasks for analysis"""
        if not self.failed_tasks:
            logger.info("[REPORT] No failed tasks to report")
            return

        report_data = []
        for task in self.failed_tasks:
            report_data.append({
                'task_id': task.task_id,
                'symbol': task.symbol,
                'underlying': task.underlying,
                'strike_price': task.strike_price,
                'option_type': task.option_type,
                'retry_count': task.retry_count,
                'last_error': task.last_error,
                'created_at': task.created_at
            })

        # Save as JSON for easy analysis
        report_file = output_path / f"failed_downloads_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)

        logger.info(f"[REPORT] Saved failed tasks report to {report_file}")
        return report_file
