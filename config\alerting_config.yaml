# Alerting and Notifications Configuration
# Comprehensive alerting system configuration

# Notification Channels Configuration
notifications:
  # Console notifications (always enabled for development)
  console:
    enabled: true
    log_level: "INFO"              # Log level for console output
    colored_output: true           # Use colored console output
    include_emoji: true            # Include emoji in console messages
  
  # File-based notifications
  file:
    enabled: true
    log_file: "logs/alerts.log"    # Alert log file path
    max_size_mb: 100               # Maximum log file size in MB
    backup_count: 5                # Number of backup files to keep
    rotation_enabled: true         # Enable log rotation
    format: "json"                 # Log format: json, text
    include_stack_trace: true      # Include stack traces for errors
  
  # Email notifications
  email:
    enabled: false                 # Enable/disable email notifications
    smtp_server: "smtp.gmail.com"  # SMTP server
    smtp_port: 587                 # SMTP port
    use_tls: true                  # Use TLS encryption
    username: ""                   # Email username
    password: ""                   # Email password (use app password for Gmail)
    from_email: ""                 # From email address
    from_name: "Options Trading Bot"  # From name
    to_emails: []                  # List of recipient email addresses
    
    # Email templates
    templates:
      subject_prefix: "[Trading Alert]"
      include_logo: false
      html_format: true
      
    # Email rate limiting
    max_emails_per_hour: 50
    batch_similar_alerts: true
    batch_interval_minutes: 5
  
  # Telegram notifications
  telegram:
    enabled: false                 # Enable/disable Telegram notifications
    bot_token: ""                  # Telegram bot token
    chat_ids: []                   # List of chat IDs to send messages to
    
    # Message formatting
    parse_mode: "Markdown"         # Message parse mode: Markdown, HTML
    disable_web_page_preview: true
    disable_notification: false    # Silent notifications
    
    # Telegram-specific settings
    max_message_length: 4096
    split_long_messages: true
    include_charts: false          # Include chart images (future feature)
  
  # Slack notifications
  slack:
    enabled: false                 # Enable/disable Slack notifications
    webhook_url: ""                # Slack webhook URL
    channel: "#trading-alerts"     # Default channel
    username: "Trading Bot"        # Bot username
    icon_emoji: ":chart_with_upwards_trend:"  # Bot icon
    
    # Message formatting
    include_attachments: true
    color_code_by_priority: true
    mention_on_critical: false     # Mention @channel on critical alerts
    thread_similar_alerts: true
  
  # SMS notifications (future feature)
  sms:
    enabled: false
    provider: "twilio"             # SMS provider
    account_sid: ""
    auth_token: ""
    from_number: ""
    to_numbers: []

# Alert Rules Configuration
alert_rules:
  # Signal alerts
  signal_alerts:
    enabled: true
    min_confidence: 0.7            # Minimum confidence to trigger alert
    max_alerts_per_hour: 20        # Maximum signal alerts per hour
    channels: ["console", "file"]  # Default channels for signal alerts
    
    # Signal-specific rules
    high_confidence_threshold: 0.85  # Threshold for high-priority alerts
    critical_confidence_threshold: 0.95  # Threshold for critical alerts
    include_strategy_details: true
    include_market_context: true
    
    # Filter rules
    filter_duplicate_symbols: true
    duplicate_window_minutes: 10
    min_price_change_percent: 1.0  # Minimum price change to alert
  
  # Risk alerts
  risk_alerts:
    enabled: true
    channels: ["console", "file", "email"]
    
    # Risk thresholds
    loss_threshold_percent: 5.0    # Alert when loss exceeds 5%
    position_limit_percent: 80.0   # Alert when position limit reaches 80%
    margin_threshold_percent: 20.0 # Alert when margin falls below 20%
    drawdown_threshold_percent: 10.0  # Alert on 10% drawdown
    
    # Portfolio risk limits
    max_portfolio_exposure: 90.0   # Maximum portfolio exposure %
    max_sector_concentration: 30.0 # Maximum sector concentration %
    max_single_position: 15.0      # Maximum single position %
    
    # Risk escalation
    escalate_critical_risks: true
    critical_risk_channels: ["console", "file", "email", "telegram"]
    auto_acknowledge_resolved: true
  
  # Performance alerts
  performance_alerts:
    enabled: true
    channels: ["console", "file"]
    
    # Profit milestones (in INR)
    profit_milestones: [1000, 2500, 5000, 10000, 25000, 50000]
    loss_milestones: [-500, -1000, -2500, -5000, -10000]
    
    # Performance thresholds
    daily_target_achievement: 5000  # Daily profit target
    weekly_target_achievement: 25000  # Weekly profit target
    win_rate_threshold: 60.0       # Alert if win rate drops below 60%
    
    # Streak alerts
    winning_streak_milestone: 5    # Alert on 5+ winning trades
    losing_streak_milestone: 3     # Alert on 3+ losing trades
    
    # Performance degradation alerts
    performance_degradation_threshold: 20.0  # Alert on 20% performance drop
    sharpe_ratio_threshold: 1.0    # Alert if Sharpe ratio drops below 1.0
  
  # System alerts
  system_alerts:
    enabled: true
    channels: ["console", "file", "email"]
    
    # System health thresholds
    error_threshold: 5             # Alert after 5 consecutive errors
    response_time_threshold_ms: 5000  # Alert if response time > 5s
    memory_usage_threshold_mb: 1000   # Alert if memory usage > 1GB
    cpu_usage_threshold_percent: 80   # Alert if CPU usage > 80%
    
    # Agent health monitoring
    agent_heartbeat_timeout_minutes: 5  # Alert if agent doesn't respond
    data_freshness_threshold_minutes: 10  # Alert if data is stale
    
    # Connection monitoring
    api_connection_failures: 3     # Alert after 3 connection failures
    data_feed_interruption_minutes: 2  # Alert if data feed interrupted
    
    # System recovery alerts
    alert_on_recovery: true
    include_recovery_metrics: true

# Rate Limiting Configuration
rate_limiting:
  enabled: true
  
  # Global rate limits
  max_alerts_per_minute: 10       # Maximum alerts per minute (all types)
  max_alerts_per_hour: 100        # Maximum alerts per hour (all types)
  max_alerts_per_day: 500         # Maximum alerts per day (all types)
  
  # Per-type rate limits
  signal_alerts_per_hour: 30
  risk_alerts_per_hour: 20
  performance_alerts_per_hour: 15
  system_alerts_per_hour: 25
  
  # Duplicate suppression
  duplicate_suppression_minutes: 5  # Suppress duplicate alerts for 5 minutes
  similarity_threshold: 0.8       # Threshold for considering alerts similar
  
  # Burst handling
  burst_limit: 20                 # Allow burst of 20 alerts
  burst_window_seconds: 60        # Burst window duration
  
  # Rate limit actions
  action_on_limit_exceeded: "suppress"  # Options: suppress, delay, escalate
  escalate_to_channels: ["email"]  # Channels to escalate when limits exceeded

# Alert Filtering and Processing
filtering:
  # Content filtering
  filter_profanity: false
  filter_sensitive_data: true     # Filter out sensitive information
  
  # Priority filtering
  min_priority_console: "LOW"     # Minimum priority for console alerts
  min_priority_file: "LOW"        # Minimum priority for file alerts
  min_priority_email: "MEDIUM"    # Minimum priority for email alerts
  min_priority_telegram: "HIGH"   # Minimum priority for Telegram alerts
  
  # Time-based filtering
  quiet_hours_enabled: false      # Enable quiet hours
  quiet_hours_start: "22:00"      # Quiet hours start time
  quiet_hours_end: "08:00"        # Quiet hours end time
  quiet_hours_channels: ["email", "telegram"]  # Channels affected by quiet hours
  
  # Market hours filtering
  market_hours_only: false        # Only send alerts during market hours
  weekend_alerts: true            # Send alerts on weekends
  holiday_alerts: true            # Send alerts on holidays

# Alert Persistence and History
persistence:
  # Database settings
  save_to_database: false         # Save alerts to database
  database_url: ""                # Database connection URL
  
  # File persistence
  save_to_file: true              # Save alerts to file
  history_file: "data/alerts/alert_history.json"
  max_history_size_mb: 50         # Maximum history file size
  
  # Retention settings
  retention_days: 30              # Keep alerts for 30 days
  auto_cleanup: true              # Automatically clean up old alerts
  cleanup_interval_hours: 24      # Cleanup interval
  
  # Backup settings
  backup_enabled: true
  backup_interval_hours: 6
  backup_location: "backups/alerts"
  max_backup_files: 10

# Integration Settings
integration:
  # Risk management integration
  risk_manager_integration: true
  sync_risk_alerts: true
  
  # Performance tracker integration
  performance_tracker_integration: true
  sync_performance_alerts: true
  
  # System monitor integration
  system_monitor_integration: true
  sync_system_alerts: true
  
  # External systems
  webhook_notifications: false
  webhook_url: ""
  webhook_secret: ""

# Advanced Features
advanced_features:
  # Machine learning
  ml_alert_classification: false  # Use ML to classify alert importance
  ml_spam_detection: false        # Use ML to detect spam alerts
  
  # Analytics
  alert_analytics: true           # Enable alert analytics
  trend_analysis: true            # Analyze alert trends
  pattern_recognition: false      # Recognize alert patterns
  
  # Automation
  auto_acknowledgment: false      # Auto-acknowledge certain alerts
  auto_escalation: true           # Auto-escalate unacknowledged critical alerts
  escalation_timeout_minutes: 30 # Time before escalation
  
  # Custom rules
  custom_rules_enabled: false     # Enable custom alert rules
  custom_rules_file: "config/custom_alert_rules.py"

# Testing and Development
testing:
  test_mode: false                # Enable test mode
  mock_notifications: false       # Mock notification sending
  test_alert_interval_seconds: 60  # Send test alerts every 60 seconds
  
  # Development features
  debug_mode: false               # Enable debug mode
  verbose_logging: false          # Enable verbose logging
  log_all_decisions: false        # Log all filtering decisions

# Security and Privacy
security:
  # Data protection
  encrypt_sensitive_data: false   # Encrypt sensitive alert data
  mask_personal_info: true        # Mask personal information in alerts
  
  # Access control
  require_authentication: false   # Require authentication for alert management
  api_key_required: false         # Require API key for external access
  
  # Audit trail
  audit_trail_enabled: true       # Enable audit trail
  audit_log_file: "logs/alert_audit.log"
  
  # Compliance
  gdpr_compliance: false          # Enable GDPR compliance features
  data_retention_policy: "30_days"  # Data retention policy
